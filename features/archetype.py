"""
Backend module for brand archetype and identity analysis.

This module compiles all the brand archetype, tone of voice, mission/vision analysis
functionality from multiple scripts into a single backend service. It handles comprehensive
brand personality analysis, archetype scoring, and brand identity extraction.

Functions:
- get_brand_archetype_analysis(org_url): Main function that takes org URL and returns archetype analysis
- analyze_brand_archetypes(brand_data): Analyzes brand archetypes with detailed scoring
- extract_brand_identity(url): Extracts mission, vision, tone of voice from website
- scrape_website_content(url): Scrapes content from organization website
- save_archetype_data(analysis): Saves archetype analysis to storage
- load_archetype_data(): Loads existing archetype analysis
"""

import os
import json
import re
import requests
from bs4 import BeautifulSoup
from typing import Dict, List, Any, Optional, Tuple
from dotenv import load_dotenv
from openai import OpenAI
from pydantic import BaseModel, Field

# Load environment variables
load_dotenv()


class ArchetypeScore(BaseModel):
    """Model for archetype score and reasoning."""
    score: float = Field(description="Percentage score (0-100) for this archetype")
    reasoning: str = Field(description="Detailed reasoning for this score")


class ArchetypeAnalysis(BaseModel):
    """Model for complete archetype analysis results."""
    archetype_scores: Dict[str, ArchetypeScore] = Field(description="Scores and reasoning for each archetype")
    primary_archetype: str = Field(description="The primary archetype with the highest score")
    primary_reasoning: str = Field(description="Detailed reasoning for the primary archetype")


class WebScrapingTool:
    """Tool for scraping web content from organization websites."""
    
    def __init__(self):
        self.name = "Web Scraper"
        self.description = "Scrapes content from websites for brand identity analysis"
    
    def scrape_content(self, url: str) -> str:
        """
        Scrape content from the given URL.
        
        Args:
            url (str): The URL to scrape
            
        Returns:
            str: Scraped text content from the website
        """
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
                
            # Get text content
            text = soup.get_text(separator='\n', strip=True)
            
            # Clean up text
            lines = [line.strip() for line in text.splitlines() if line.strip()]
            content = '\n'.join(lines)
            
            return content
            
        except Exception as e:
            return f"Error scraping URL: {str(e)}"


class BrandIdentityExtractor:
    """Tool for extracting brand identity elements from website content."""
    
    def __init__(self):
        self.name = "Brand Identity Extractor"
        self.description = "Extracts mission, vision, tone of voice from website content"
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    
    def extract_identity(self, content: str, url: str) -> Dict[str, Any]:
        """
        Extract brand identity elements from website content.
        
        Args:
            content (str): Scraped website content
            url (str): Website URL
            
        Returns:
            Dict[str, Any]: Dictionary containing brand identity elements
        """
        try:
            response = self.client.chat.completions.create(
                model="gpt-4o-mini-2024-07-18",
                messages=[
                    {
                        "role": "system",
                        "content": """You are a brand identity expert who analyzes website content to extract key brand elements.

                        Extract the following from the provided content:
                        1. Mission Statement: The company's purpose and reason for existence
                        2. Vision Statement: The company's future aspirations and goals
                        3. Tone of Voice: 3-4 descriptive words that capture the brand's communication style
                        4. Brand Personality Traits: Key characteristics that define the brand's personality
                        5. Core Values: Fundamental beliefs and principles the company stands for

                        Look for explicit statements like "Our mission is...", "We believe...", "Our vision...", etc.
                        Also analyze the overall writing style, word choice, and messaging approach to determine tone.

                        Return the information in JSON format with these exact keys:
                        {
                            "mission": "Mission statement if found, otherwise reasonable inference",
                            "vision": "Vision statement if found, otherwise reasonable inference", 
                            "tone_of_voice": "3-4 tone descriptors separated by commas",
                            "brand_personality_traits": ["trait1", "trait2", "trait3"],
                            "core_values": ["value1", "value2", "value3"],
                            "organization_url": "provided URL"
                        }

                        If specific statements aren't found, make reasonable inferences based on the content and messaging style.
                        Never return null or empty values - always provide meaningful defaults."""
                    },
                    {
                        "role": "user",
                        "content": f"Analyze this website content from {url} and extract brand identity elements:\n\n{content[:4000]}"
                    }
                ],
                temperature=0.3,
                max_tokens=1024
            )
            
            try:
                json_str = response.choices[0].message.content.strip()
                json_match = re.search(r'\{.*\}', json_str, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                json_output = json.loads(json_str)
                
                # Set default values for missing fields
                default_output = {
                    "mission": "Information not available from website content",
                    "vision": "Information not available from website content",
                    "tone_of_voice": "Professional, Informative, Engaging",
                    "brand_personality_traits": ["Professional", "Reliable", "Innovative"],
                    "core_values": ["Quality", "Innovation", "Customer Focus"],
                    "organization_url": url
                }
                
                for key in default_output:
                    if key not in json_output or not json_output[key]:
                        json_output[key] = default_output[key]
                
                # Ensure brand_personality_traits and core_values are lists
                for list_field in ["brand_personality_traits", "core_values"]:
                    if not isinstance(json_output[list_field], list):
                        if isinstance(json_output[list_field], str):
                            json_output[list_field] = [trait.strip() for trait in json_output[list_field].split(',')]
                        else:
                            json_output[list_field] = default_output[list_field]
                
                return json_output
                
            except (json.JSONDecodeError, AttributeError) as e:
                return {
                    "mission": "Error processing mission statement",
                    "vision": "Error processing vision statement", 
                    "tone_of_voice": "Professional, Informative, Engaging",
                    "brand_personality_traits": ["Professional", "Reliable"],
                    "core_values": ["Quality", "Innovation"],
                    "organization_url": url,
                    "error": f"Error processing brand identity: {str(e)}"
                }
            
        except Exception as e:
            return {
                "mission": "Failed to analyze mission statement",
                "vision": "Failed to analyze vision statement",
                "tone_of_voice": "Professional, Informative, Engaging", 
                "brand_personality_traits": ["Professional", "Reliable"],
                "core_values": ["Quality", "Innovation"],
                "organization_url": url,
                "error": f"Failed to extract brand identity: {str(e)}"
            }


class ArchetypeAnalyzer:
    """Analyzer for brand archetypes with comprehensive scoring."""
    
    def __init__(self):
        """Initialize the archetype analyzer."""
        self.archetypes = [
            "Creator", "Sage", "Caregiver", "Innocent", "Jester", "Magician", 
            "Ruler", "Hero", "Everyman", "Rebel", "Explorer", "Lover"
        ]
        self.archetype_descriptions = {
            "Creator": "Innovates to build original, lasting products or experiences that express their vision.",
            "Sage": "Seeks truth and wisdom to enlighten others through knowledge and insight.",
            "Caregiver": "Protects and nurtures others with compassion and selflessness.",
            "Innocent": "Spreads joy and optimism by living simply and doing what's right.",
            "Jester": "Brings happiness through humor, fun, and lightheartedness.",
            "Magician": "Transforms reality to create awe-inspiring, dream-like experiences.",
            "Ruler": "Leads with authority and order to achieve control, success, and stability.",
            "Hero": "Strives to overcome challenges and inspire through courage and determination.",
            "Everyman": "Relatable and grounded, values connection and belonging for all.",
            "Rebel": "Challenges norms to spark change and revolution with bold independence.",
            "Explorer": "Embarks on adventures to discover new experiences and personal freedom.",
            "Lover": "Pursues deep emotional and physical connections through passion and desire."
        }
        self.archetype_traits = {
            "Creator": ["innovative", "creative", "artistic", "inventive", "expressive", "original", "visionary"],
            "Sage": ["wise", "knowledgeable", "analytical", "intelligent", "thoughtful", "informative", "expert"],
            "Caregiver": ["nurturing", "supportive", "compassionate", "helpful", "generous", "protective", "empathetic"],
            "Innocent": ["optimistic", "pure", "honest", "simple", "moral", "ethical", "trustworthy"],
            "Jester": ["playful", "humorous", "fun", "entertaining", "light-hearted", "spontaneous", "joyful"],
            "Magician": ["transformative", "mystical", "inspiring", "visionary", "charismatic", "powerful", "imaginative"],
            "Ruler": ["authoritative", "powerful", "controlling", "structured", "organized", "prestigious", "leading"],
            "Hero": ["brave", "determined", "skilled", "inspiring", "strong", "courageous", "triumphant"],
            "Everyman": ["relatable", "authentic", "grounded", "inclusive", "approachable", "friendly", "down-to-earth"],
            "Rebel": ["disruptive", "revolutionary", "unconventional", "bold", "provocative", "independent", "challenging"],
            "Explorer": ["adventurous", "free", "independent", "curious", "pioneering", "daring", "discovering"],
            "Lover": ["passionate", "sensual", "intimate", "romantic", "attractive", "beautiful", "emotional"]
        }
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    
    def analyze_brand_content(self, brand_data: Dict[str, Any]) -> ArchetypeAnalysis:
        """
        Analyze brand content to determine archetype alignment.
        
        Args:
            brand_data: Dictionary containing brand information
                (mission, vision, personality, tone, etc.)
                
        Returns:
            ArchetypeAnalysis object with scores and reasoning
        """
        # Extract relevant brand information
        mission = brand_data.get("mission", "")
        vision = brand_data.get("vision", "")
        brand_personality = brand_data.get("brand_personality", "")
        tone_of_voice = brand_data.get("tone_of_voice", "")
        
        # Combine all text for analysis
        combined_text = f"{mission} {vision} {brand_personality} {tone_of_voice}".lower()
        
        # If we have enough content, use AI for analysis
        if len(combined_text) > 50:
            return self._analyze_with_ai(brand_data)
        else:
            # Fallback to trait-based analysis if not enough content
            return self._analyze_with_traits(combined_text)

    def _analyze_with_traits(self, text: str) -> ArchetypeAnalysis:
        """
        Analyze brand content using trait matching.

        Args:
            text: Combined brand text for analysis

        Returns:
            ArchetypeAnalysis object with scores and reasoning
        """
        scores = {}

        # Calculate scores based on trait matching
        for archetype, traits in self.archetype_traits.items():
            trait_matches = 0
            matching_traits = []

            for trait in traits:
                if trait.lower() in text.lower():
                    trait_matches += 1
                    matching_traits.append(trait)

            # Calculate percentage score (0-100)
            if traits:
                percentage = min(100, (trait_matches / len(traits)) * 100)
            else:
                percentage = 0

            # Generate reasoning based on matching traits
            if matching_traits:
                reasoning = f"The brand exhibits {len(matching_traits)} traits associated with the {archetype} archetype: {', '.join(matching_traits)}."
            else:
                reasoning = f"The brand shows limited alignment with the {archetype} archetype traits."

            scores[archetype] = ArchetypeScore(score=percentage, reasoning=reasoning)

        # Find primary archetype (highest score)
        primary_archetype = max(scores.items(), key=lambda x: x[1].score)

        return ArchetypeAnalysis(
            archetype_scores=scores,
            primary_archetype=primary_archetype[0],
            primary_reasoning=primary_archetype[1].reasoning
        )

    def _analyze_with_ai(self, brand_data: Dict[str, Any]) -> ArchetypeAnalysis:
        """
        Analyze brand content using AI.

        Args:
            brand_data: Dictionary containing brand information

        Returns:
            ArchetypeAnalysis object with scores and reasoning
        """
        try:
            # Prepare the prompt with brand information
            prompt = self._prepare_analysis_prompt(brand_data)

            # Call OpenAI API
            response = self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {
                        "role": "system",
                        "content": """You are a brand archetype expert who analyzes brand content to determine alignment with the 12 brand archetypes.
                        For each archetype, provide:
                        1. A percentage score (0-100%) indicating how well the brand aligns with that archetype
                        2. Detailed reasoning explaining why the brand does or doesn't align with that archetype

                        Base your analysis on the brand's mission, vision, personality, tone of voice, and other provided information.
                        Be specific and reference actual elements from the brand in your reasoning.
                        """
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=1500,
                response_format={"type": "json_object"}
            )

            # Parse the response
            result = json.loads(response.choices[0].message.content)

            # Convert to our format
            scores = {}
            for archetype in self.archetypes:
                if archetype in result:
                    arch_data = result[archetype]
                    # Extract score as a percentage (0-100)
                    if isinstance(arch_data, dict) and "score" in arch_data and "reasoning" in arch_data:
                        # Handle different score formats
                        score_str = str(arch_data["score"]).strip().rstrip("%")
                        try:
                            score = float(score_str)
                        except ValueError:
                            # Default to 50 if we can't parse the score
                            score = 50

                        scores[archetype] = ArchetypeScore(
                            score=score,
                            reasoning=arch_data["reasoning"]
                        )
                    else:
                        scores[archetype] = ArchetypeScore(
                            score=0.0,
                            reasoning=f"No analysis provided for {archetype} archetype"
                        )
                else:
                    scores[archetype] = ArchetypeScore(
                        score=0.0,
                        reasoning=f"No analysis provided for {archetype} archetype"
                    )

            # If no scores were parsed, use trait-based analysis as fallback
            if not scores:
                return self._analyze_with_traits(
                    f"{brand_data.get('mission', '')} {brand_data.get('vision', '')} {brand_data.get('brand_personality', '')} {brand_data.get('tone_of_voice', '')}"
                )

            # Find primary archetype (highest score)
            primary_archetype = max(scores.items(), key=lambda x: x[1].score)

            return ArchetypeAnalysis(
                archetype_scores=scores,
                primary_archetype=primary_archetype[0],
                primary_reasoning=primary_archetype[1].reasoning
            )

        except Exception as e:
            # Fallback to trait-based analysis on error
            return self._analyze_with_traits(
                f"{brand_data.get('mission', '')} {brand_data.get('vision', '')} {brand_data.get('brand_personality', '')} {brand_data.get('tone_of_voice', '')}"
            )

    def _prepare_analysis_prompt(self, brand_data: Dict[str, Any]) -> str:
        """
        Prepare the analysis prompt for AI processing.

        Args:
            brand_data: Dictionary containing brand information

        Returns:
            Formatted prompt string
        """
        # Extract brand information
        organization_url = brand_data.get("organization_url", "")
        mission = brand_data.get("mission", "")
        vision = brand_data.get("vision", "")
        brand_personality = brand_data.get("brand_personality", "")
        tone_of_voice = brand_data.get("tone_of_voice", "")
        brand_personality_traits = brand_data.get("brand_personality_traits", [])
        core_values = brand_data.get("core_values", [])

        # Build the prompt
        prompt = f"""Analyze the following brand information and determine the alignment with each of the 12 brand archetypes.

        Brand URL: {organization_url}
        Mission: {mission}
        Vision: {vision}
        Brand Personality: {brand_personality}
        Tone of Voice: {tone_of_voice}

        For each of the following archetypes, provide:
        1. A percentage score (0-100%) indicating how well the brand aligns with that archetype
        2. Detailed reasoning explaining why the brand does or doesn't align with that archetype

        The 12 archetypes are:
        """

        # Add archetype descriptions
        for archetype, description in self.archetype_descriptions.items():
            prompt += f"\n- {archetype}: {description}"

        prompt += """

        Return your analysis as a JSON object with this structure:
        {
            "Creator": {"score": 75, "reasoning": "Detailed reasoning..."},
            "Sage": {"score": 45, "reasoning": "Detailed reasoning..."},
            ...
        }

        Be specific in your reasoning and reference actual elements from the brand information provided.
        """

        return prompt


# Main functions
def scrape_website_content(url: str) -> str:
    """
    Scrape content from organization website.

    Args:
        url (str): Organization website URL

    Returns:
        str: Scraped website content
    """
    scraper = WebScrapingTool()
    return scraper.scrape_content(url)


def extract_brand_identity(url: str) -> Dict[str, Any]:
    """
    Extract brand identity elements from website.

    Args:
        url (str): Organization website URL

    Returns:
        Dict[str, Any]: Brand identity including mission, vision, tone of voice, etc.
    """
    # Step 1: Scrape website content
    content = scrape_website_content(url)

    if content.startswith("Error scraping URL:"):
        return {
            "mission": "Could not extract from website",
            "vision": "Could not extract from website",
            "tone_of_voice": "Professional, Informative, Engaging",
            "brand_personality_traits": ["Professional", "Reliable"],
            "core_values": ["Quality", "Innovation"],
            "organization_url": url,
            "error": content
        }

    # Step 2: Extract brand identity
    extractor = BrandIdentityExtractor()
    return extractor.extract_identity(content, url)


def analyze_brand_archetypes(brand_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze brand archetypes for the given brand data.

    Args:
        brand_data: Dictionary containing brand information

    Returns:
        Dictionary with archetype scores and primary archetype
    """
    analyzer = ArchetypeAnalyzer()
    analysis = analyzer.analyze_brand_content(brand_data)

    # Convert to dictionary format
    result = {
        "archetype_scores": {},
        "primary_archetype": analysis.primary_archetype,
        "primary_reasoning": analysis.primary_reasoning
    }

    # Format scores as dictionaries
    for archetype, score_obj in analysis.archetype_scores.items():
        result["archetype_scores"][archetype] = {
            "score": score_obj.score,
            "reasoning": score_obj.reasoning
        }

    return result


def get_brand_archetype_analysis(org_url: str) -> Dict[str, Any]:
    """
    Main function that takes organization URL and returns complete brand archetype analysis.

    This function:
    1. Scrapes content from the organization website
    2. Extracts brand identity elements (mission, vision, tone of voice)
    3. Analyzes brand archetypes with detailed scoring
    4. Returns structured archetype analysis data

    Args:
        org_url (str): Organization website URL

    Returns:
        Dict[str, Any]: Complete brand archetype analysis containing:
            - Brand identity: mission, vision, tone_of_voice, brand_personality_traits, core_values
            - Archetype analysis: archetype_scores with detailed reasoning for all 12 archetypes
            - Primary archetype: primary_archetype and primary_reasoning
            - organization_url: Original URL provided
    """
    try:
        # Step 1: Extract brand identity from website
        brand_identity = extract_brand_identity(org_url)

        # Step 2: Analyze brand archetypes
        archetype_analysis = analyze_brand_archetypes(brand_identity)

        # Step 3: Combine results
        complete_analysis = {
            **brand_identity,  # Include all brand identity elements
            **archetype_analysis,  # Include archetype analysis
            "organization_url": org_url
        }

        # Step 4: Add brand personality from primary archetype for compatibility
        complete_analysis["brand_personality"] = archetype_analysis["primary_archetype"]
        complete_analysis["brand_personality_reasoning"] = archetype_analysis["primary_reasoning"]

        return complete_analysis

    except Exception as e:
        return {
            "mission": "Error occurred during analysis",
            "vision": "Error occurred during analysis",
            "tone_of_voice": "Professional, Informative, Engaging",
            "brand_personality_traits": ["Professional", "Reliable"],
            "core_values": ["Quality", "Innovation"],
            "archetype_scores": {},
            "primary_archetype": "Unknown",
            "primary_reasoning": f"Failed to analyze brand archetype: {str(e)}",
            "brand_personality": "Unknown",
            "brand_personality_reasoning": f"Failed to analyze brand archetype: {str(e)}",
            "organization_url": org_url,
            "error": str(e)
        }


# Data management functions
def save_archetype_data(analysis: Dict[str, Any], organization_url: Optional[str] = None) -> bool:
    """
    Save archetype analysis to the data storage file.

    Args:
        analysis (Dict[str, Any]): Archetype analysis to save
        organization_url (str, optional): URL of the organization

    Returns:
        bool: True if save successful, False otherwise
    """
    if not analysis:
        return False

    if not organization_url:
        organization_url = analysis.get('organization_url')
        if not organization_url:
            return False

    try:
        # Ensure analysis has organization_url
        analysis['organization_url'] = organization_url

        # Load existing analyses
        all_analyses = load_archetype_data()

        # Update analysis for this organization
        all_analyses[organization_url] = analysis

        # Ensure data directory exists
        os.makedirs('data', exist_ok=True)

        # Save back to file
        data_file_path = os.path.join('data', 'brand_archetype_analysis.json')
        with open(data_file_path, 'w') as f:
            json.dump(all_analyses, f, indent=2)

        return True

    except Exception as e:
        print(f"Error saving archetype analysis: {str(e)}")
        return False


def load_archetype_data(organization_url: Optional[str] = None) -> Dict[str, Any]:
    """
    Load archetype analysis for a specific organization or all organizations.

    Args:
        organization_url (str, optional): URL of the organization to load.
            If None, returns all organizations as a dictionary.

    Returns:
        Dict[str, Any]: Archetype analysis for a specific organization or all organizations
    """
    data_file_path = os.path.join('data', 'brand_archetype_analysis.json')

    # Default archetype analysis
    default_analysis = {
        "mission": "Not analyzed",
        "vision": "Not analyzed",
        "tone_of_voice": "Professional, Informative, Engaging",
        "brand_personality_traits": ["Professional", "Reliable"],
        "core_values": ["Quality", "Innovation"],
        "archetype_scores": {},
        "primary_archetype": "Unknown",
        "primary_reasoning": "Analysis not performed",
        "brand_personality": "Unknown",
        "brand_personality_reasoning": "Analysis not performed",
        "organization_url": ""
    }

    try:
        if os.path.exists(data_file_path):
            with open(data_file_path, 'r') as f:
                all_analyses = json.load(f)

                if organization_url:
                    return all_analyses.get(organization_url, default_analysis)
                else:
                    return all_analyses
        else:
            if organization_url:
                return default_analysis
            else:
                return {'default_organization': default_analysis}
    except (FileNotFoundError, json.JSONDecodeError):
        if organization_url:
            return default_analysis
        else:
            return {'default_organization': default_analysis}


def get_all_archetype_analyses() -> Dict[str, Dict[str, Any]]:
    """
    Get all archetype analyses for all organizations.

    Returns:
        Dict[str, Dict[str, Any]]: Dictionary mapping organization URLs to their archetype analyses
    """
    return load_archetype_data()


def update_archetype_data(organization_url: str, updates: Dict[str, Any]) -> bool:
    """
    Update specific fields in archetype analysis for an organization.

    Args:
        organization_url (str): URL of the organization
        updates (Dict[str, Any]): Fields to update

    Returns:
        bool: True if update successful, False otherwise
    """
    try:
        # Load existing analysis
        existing_analysis = load_archetype_data(organization_url)

        # Update with new values
        existing_analysis.update(updates)
        existing_analysis['organization_url'] = organization_url

        # Save updated analysis
        return save_archetype_data(existing_analysis, organization_url)

    except Exception as e:
        print(f"Error updating archetype analysis: {str(e)}")
        return False


def delete_archetype_data(organization_url: str) -> bool:
    """
    Delete archetype analysis for a specific organization.

    Args:
        organization_url (str): URL of the organization

    Returns:
        bool: True if deletion successful, False otherwise
    """
    try:
        all_analyses = load_archetype_data()

        if organization_url in all_analyses:
            del all_analyses[organization_url]

            # Save updated analyses
            data_file_path = os.path.join('data', 'brand_archetype_analysis.json')
            with open(data_file_path, 'w') as f:
                json.dump(all_analyses, f, indent=2)

            return True
        else:
            return False  # Organization not found

    except Exception as e:
        print(f"Error deleting archetype analysis: {str(e)}")
        return False


# Utility functions
def get_archetype_description(archetype: str) -> str:
    """
    Get description for a specific archetype.

    Args:
        archetype (str): Name of the archetype

    Returns:
        str: Description of the archetype
    """
    analyzer = ArchetypeAnalyzer()
    return analyzer.archetype_descriptions.get(archetype, "Unknown archetype")


def get_all_archetypes() -> List[str]:
    """
    Get list of all available archetypes.

    Returns:
        List[str]: List of archetype names
    """
    analyzer = ArchetypeAnalyzer()
    return analyzer.archetypes


def get_archetype_traits(archetype: str) -> List[str]:
    """
    Get traits associated with a specific archetype.

    Args:
        archetype (str): Name of the archetype

    Returns:
        List[str]: List of traits for the archetype
    """
    analyzer = ArchetypeAnalyzer()
    return analyzer.archetype_traits.get(archetype, [])


# Example usage and testing function
def test_archetype_analysis(test_url: str = "https://www.analyticsvidhya.com/") -> None:
    """
    Test function to demonstrate brand archetype analysis.

    Args:
        test_url (str): URL to test with (defaults to Analytics Vidhya)
    """
    print(f"Testing brand archetype analysis for: {test_url}")
    print("-" * 50)

    # Get complete archetype analysis
    analysis = get_brand_archetype_analysis(test_url)

    # Display results
    print("Brand Identity Analysis Results:")
    print(f"Mission: {analysis.get('mission', 'N/A')}")
    print(f"Vision: {analysis.get('vision', 'N/A')}")
    print(f"Tone of Voice: {analysis.get('tone_of_voice', 'N/A')}")
    print(f"Brand Personality Traits: {analysis.get('brand_personality_traits', [])}")
    print(f"Core Values: {analysis.get('core_values', [])}")

    print("\nArchetype Analysis Results:")
    print(f"Primary Archetype: {analysis.get('primary_archetype', 'N/A')}")
    print(f"Primary Reasoning: {analysis.get('primary_reasoning', 'N/A')}")

    # Display top 3 archetype scores
    archetype_scores = analysis.get('archetype_scores', {})
    if archetype_scores:
        sorted_scores = sorted(archetype_scores.items(), key=lambda x: x[1]['score'], reverse=True)
        print("\nTop 3 Archetype Scores:")
        for i, (archetype, score_data) in enumerate(sorted_scores[:3], 1):
            print(f"{i}. {archetype}: {score_data['score']:.1f}%")
            print(f"   Reasoning: {score_data['reasoning'][:100]}...")

    # Save the data
    if save_archetype_data(analysis, test_url):
        print("\n✅ Archetype analysis saved successfully!")
    else:
        print("\n❌ Failed to save archetype analysis!")


if __name__ == "__main__":
    # Test the module
    test_archetype_analysis()
