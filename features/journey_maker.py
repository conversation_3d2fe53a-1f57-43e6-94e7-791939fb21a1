"""
Journey Maker Backend Script
Aggregates all journey tree functionality exactly from the original scripts.

This script combines functionality from:
- src/openengage/core/journey_builder.py (journey tree functions)
- src/openengage/utils/file_utils.py (node email and logging functions)

Inputs:
- stages: List of journey stages
- start_idx: Starting stage index
- highlighted_node: Node to highlight (optional)
- journey_steps: List of journey step dictionaries
- node_id: Node identifier for email operations

Outputs:
- Plotly figure objects for journey tree and flow visualizations
- Node email content and logging operations
"""
import plotly.graph_objects as go
from datetime import datetime
import os
import json

def create_journey_tree_plotly(stages, start_idx, highlighted_node=None):
    """Create an interactive journey tree using Plotly"""
    # Fixed node positions for perfect binary tree with depth=2
    node_positions = {
        1: (0, 3),    # Root
        2: (-1, 2),   # Left child of root
        3: (1, 2),    # Right child of root
        4: (-1.5, 1), # Left child of node 2
        5: (-0.5, 1), # Right child of node 2
        6: (0.5, 1),  # Left child of node 3
        7: (1.5, 1)   # Right child of node 3
    }

    # If no node is highlighted, highlight root node
    if highlighted_node is None:
        highlighted_node = 1

    # Define edges for binary tree
    edges = [(1, 2), (1, 3), (2, 4), (2, 5), (3, 6), (3, 7)]

    # Map stages to tree structure
    current_stage = stages[start_idx]
    next_stage = stages[start_idx + 1] if start_idx + 1 < len(stages) else None
    final_stage = stages[start_idx + 2] if start_idx + 2 < len(stages) else None

    # Create node mapping with stages and colors
    node_mapping = {
        1: (current_stage, "#2ECC71", "Current Stage: User is here"),  # Root - Green
        2: (current_stage, "#E74C3C", "Not Achieved: User opened/clicked but hasn't reached goal"),  # Left - Red
        3: (next_stage, "#F1C40F", "Next Stage: User achieved the goal"),     # Right - Yellow
        4: (current_stage, "#E74C3C", "Not Achieved: User opened/clicked but hasn't reached goal"),
        5: (next_stage, "#F1C40F", "Next Stage: User achieved the goal"),
        6: (next_stage, "#E74C3C", "Not Achieved: User opened/clicked but hasn't reached goal"),
        7: (final_stage, "#F1C40F", "Next Stage: User achieved the goal")
    }

    # Create edge traces
    edge_x, edge_y = [], []
    for edge in edges:
        x0, y0 = node_positions[edge[0]]
        x1, y1 = node_positions[edge[1]]
        mid_x = (x0 + x1) / 2
        mid_y = (y0 + y1) / 2 - 0.2
        edge_x.extend([x0, mid_x, x1, None])
        edge_y.extend([y0, mid_y, y1, None])

    edge_trace = go.Scatter(
        x=edge_x, y=edge_y,
        mode='lines',
        line=dict(width=2, color='#888'),
        hoverinfo='none'
    )

    # Create node trace
    node_x, node_y, node_colors, hover_text = [], [], [], []
    node_ids = []

    for node_num in range(1, 8):
        if node_num in node_mapping and node_mapping[node_num][0]:
            stage, color, hover_info = node_mapping[node_num]
            x, y = node_positions[node_num]
            node_x.append(x)
            node_y.append(y)

            # Highlight selected node with a thicker border
            if node_num == highlighted_node:
                line_width = 4
                line_color = '#000'
            else:
                line_width = 2
                line_color = 'white'

            node_colors.append(color)
            hover_text.append(f"{hover_info}<br>Stage: {stage}")
            node_ids.append(f"node_{node_num}")

    node_trace = go.Scatter(
        x=node_x, y=node_y,
        mode='markers+text',
        text=[''] * len(node_x),
        textposition="middle center",
        hoverinfo='text',
        hovertext=hover_text,
        customdata=node_ids,
        marker=dict(
            showscale=False,
            color=node_colors,
            size=40,
            line=dict(width=line_width, color=line_color)
        )
    )

    # Add stage labels
    annotations = []
    for node_num in range(1, 8):
        if node_num in node_mapping and node_mapping[node_num][0]:
            stage, _, _ = node_mapping[node_num]
            x, y = node_positions[node_num]
            annotations.append(
                dict(
                    x=x,
                    y=y - 0.3,
                    text=stage,
                    showarrow=False,
                    font=dict(size=10),
                    xanchor='center'
                )
            )

    fig = go.Figure(
        data=[edge_trace, node_trace],
        layout=go.Layout(
            showlegend=False,
            hovermode='closest',
            margin=dict(b=20, l=20, r=20, t=20),
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False, range=[-2, 2]),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False, range=[0, 4]),
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            width=600,
            height=500,
            clickmode='event',
            annotations=annotations
        )
    )

    return fig, node_ids


def create_user_journey_flow(journey_steps):
    """Create a visual flow of the user's actual journey"""
    if not journey_steps:
        return None

    # Create nodes and edges for the flow
    node_x, node_y, node_text, node_colors, node_sizes = [], [], [], [], []
    node_timestamps = []  # Add timestamps array
    edge_x, edge_y = [], []

    # Fixed horizontal spacing and vertical positions
    x_spacing = 2  # Increased spacing for better visibility
    y_position = 1.2  # Slightly higher to make room for timestamps

    # Track stage nodes and their positions
    stage_positions = {}  # Dict to store x-positions of stage nodes by index
    stage_indices = []  # List to store indices of stage nodes
    current_x = 0

    # First pass: Create stage nodes and calculate positions
    stage_count = 0
    for i, step in enumerate(journey_steps):
        if step['type'] == 'stage':
            x_pos = current_x
            stage_positions[i] = x_pos
            stage_indices.append(i)
            node_x.append(x_pos)
            node_y.append(y_position)
            node_text.append(step['action'])
            node_colors.append('#2ECC71')  # Green for stages
            node_sizes.append(30)  # Larger size for stage nodes
            # Format and add timestamp
            timestamp = datetime.fromisoformat(step['timestamp']).strftime("%H:%M:%S")
            node_timestamps.append(timestamp)
            current_x += x_spacing
            stage_count += 1

    # Second pass: Add event nodes
    event_positions = []  # Store event positions for edge creation
    for i, step in enumerate(journey_steps):
        if step['type'] == 'email':
            # Find the current stage this event belongs to
            current_stage_idx = max((j for j in stage_indices if j <= i), default=stage_indices[0])
            next_stage_idx = min((j for j in stage_indices if j > current_stage_idx), default=stage_indices[-1])

            # Calculate position after the current stage
            current_x = stage_positions[current_stage_idx]
            next_x = stage_positions[next_stage_idx]
            spacing = (next_x - current_x) / 3

            # Position events after the current stage node
            event_x = current_x + (spacing if step['action'] == 'Opened Email' else spacing * 2)

            # Add event node
            node_x.append(event_x)
            node_y.append(y_position)
            node_text.append('📧' if step['action'] == 'Opened Email' else '🖱️')
            node_colors.append('#000000')  # Black for events
            node_sizes.append(15)  # Smaller size for event nodes
            # Format and add timestamp
            timestamp = datetime.fromisoformat(step['timestamp']).strftime("%H:%M:%S")
            node_timestamps.append(timestamp)
            event_positions.append((event_x, y_position))

    # Create edges connecting all nodes in sequence
    all_x_positions = list(zip(node_x, node_y))
    all_x_positions.sort(key=lambda x: x[0])  # Sort by x position

    # Connect all nodes with straight lines
    for i in range(len(all_x_positions) - 1):
        current_pos = all_x_positions[i]
        next_pos = all_x_positions[i + 1]
        edge_x.extend([current_pos[0], next_pos[0], None])
        edge_y.extend([current_pos[1], next_pos[1], None])

    edge_trace = go.Scatter(
        x=edge_x, y=edge_y,
        mode='lines',
        line=dict(width=2, color='#888'),
        hoverinfo='none'
    )

    # Create node trace with text above nodes
    node_trace = go.Scatter(
        x=node_x, y=node_y,
        mode='markers+text',
        text=node_text,
        textposition="top center",
        hoverinfo='text',
        marker=dict(
            showscale=False,
            color=node_colors,
            size=node_sizes,
            line=dict(width=2, color='white')
        )
    )

    # Create timestamp trace with text below nodes
    timestamp_trace = go.Scatter(
        x=node_x,
        y=[y_position - 0.2] * len(node_y),  # Position timestamps below nodes
        mode='text',
        text=node_timestamps,
        textposition="bottom center",
        textfont=dict(
            size=10,
            color='gray'
        ),
        hoverinfo='none'
    )

    # Create figure
    fig = go.Figure(
        data=[edge_trace, node_trace, timestamp_trace],
        layout=go.Layout(
            showlegend=False,
            hovermode='closest',
            margin=dict(b=50, l=20, r=20, t=20),
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False, range=[0.5, 2]),
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)'
        )
    )

    return fig


def handle_node_click(node_id, session_data=None):
    """Handle node click events and return email content (backend version without Streamlit)"""
    if not node_id:
        return None

    email = load_node_email(node_id)

    if email:
        # Log the click
        session_id = session_data.get("session_id", "unknown") if session_data else "unknown"
        log_node_click(node_id, session_id)

        # Return email data for backend processing
        return {
            'node_id': node_id,
            'email': email,
            'node_number': node_id.split('_')[1] if '_' in node_id else node_id
        }

    return None


# File utility functions from src/openengage/utils/file_utils.py

def load_node_email(node_id):
    """Load email content for a specific node"""
    email_path = f'data/node_emails/{node_id}.json'

    if os.path.exists(email_path):
        with open(email_path, 'r') as f:
            content = json.load(f)
            return content.get("content", None)
    return None


def save_node_email(node_id, email_content):
    """Save email content for a specific node"""
    # Create emails directory if it doesn't exist
    os.makedirs('data/node_emails', exist_ok=True)

    # Save with sequential node number
    email_path = f'data/node_emails/{node_id}.json'

    # Save email content along with node mapping
    content_to_save = {
        "content": email_content,
        "node_id": node_id,
        "generated_at": datetime.now().isoformat()
    }

    with open(email_path, 'w') as f:
        json.dump(content_to_save, f, indent=2)


def log_node_click(node_id, session_id="unknown"):
    """Log node clicks to a file"""
    log_dir = "data/logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_file = os.path.join(log_dir, "click_logs.json")

    # Load existing logs
    logs = []
    if os.path.exists(log_file):
        with open(log_file, 'r') as f:
            logs = json.load(f)

    # Add new log entry
    logs.append({
        "node_id": node_id,
        "timestamp": datetime.now().isoformat(),
        "session_id": session_id
    })

    # Save updated logs
    with open(log_file, 'w') as f:
        json.dump(logs, f, indent=2)
