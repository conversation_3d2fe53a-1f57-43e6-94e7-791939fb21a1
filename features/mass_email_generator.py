"""
Mass Email Generator Backend Script
Aggregates all mass email generation functionality exactly from the original scripts.

This script combines functionality from:
- src/openengage/core/mass_email_generator.py (main mass email processing)
- src/openengage/core/batch_email_generator.py (batch processing)
- src/openengage/core/product_selector.py (product selection)
- src/openengage/core/email_formatter.py (email formatting)

Inputs:
- data_df: DataFrame containing user data
- progress_callback: Optional function to report progress
- use_batch_api: Whether to use OpenAI's Batch API

Outputs:
- DataFrame with generated email content
- HTML emails saved to data/html_emails folder
- Popup data saved to Sample Data For Mass Generation/user_popup_data.csv
"""

import os
import json
import pandas as pd
import numpy as np
import re
import yaml
from pathlib import Path
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, Callable
import logging
# Import functions from original modules
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src', 'openengage'))

try:
    from core.product_selector import select_product_for_user
    from core.email_formatter import text_to_html
except ImportError:
    # Fallback implementations if imports fail
    def select_product_for_user(user_data, products):
        """Fallback product selection"""
        if not products:
            return {}, 0.0

        # Simple selection based on last_product_sent or first product
        last_product_sent = user_data.get('last_product_sent', '')
        if last_product_sent:
            for product in products:
                if product.get('Product_Name') == last_product_sent:
                    return product, 1.0

        return products[0], 0.8

    def text_to_html(email_content, product_url=None, product_name=None,
                     communication_settings=None, recipient_email=None,
                     recipient_first_name=None, brand_guidelines=None, template_name=None):
        """Fallback HTML conversion"""
        if not email_content or not isinstance(email_content, dict):
            return ""

        content = email_content.get('content', '')
        if not content:
            return ""

        # Simple HTML conversion
        import html
        content = html.escape(content)
        content = content.replace('\n\n', '</p><p>').replace('\n', '<br>')

        if not content.startswith('<p>'):
            content = f'<p>{content}</p>'

        sender_name = communication_settings.get('sender_name', 'OpenEngage Team') if communication_settings else 'OpenEngage Team'

        html_email = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{email_content.get('subject', 'Email')}</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px;">
                <h2 style="color: #2c3e50; margin-bottom: 20px;">Hello {recipient_first_name or 'Valued Customer'}!</h2>
                {content}
                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
                    <p style="margin: 0; color: #666; font-size: 14px;">
                        Best regards,<br>
                        {sender_name}
                    </p>
                </div>
            </div>
        </body>
        </html>
        """

        return html_email

def process_mass_email_data(data_df, progress_callback=None, use_batch_api=True):
    """
    Process mass email data for generating personalized campaigns.

    Args:
        data_df (pd.DataFrame): DataFrame containing user data
        progress_callback (callable, optional): Function to report progress (current, total, message)
        use_batch_api (bool): Whether to use OpenAI's Batch API for faster processing

    Returns:
        pd.DataFrame: DataFrame with generated email content
    
    Notes:
        This function also saves user data to Sample Data For Mass Generation/user_popup_data.csv 
        with email, behavior data, target product, and user stage for each processed user.
    """

    def process_single_user(user_row, products):
        """Process a single user's data and generate email content"""
        try:
            # Check if user has behavior data
            has_behavior_data = ("Behaviour data not found" not in user_row['user_behaviour']) or ("last opened an email" in user_row['user_behaviour'])
            
            if not has_behavior_data:
                # User doesn't have behavior data, use template-based generation
                template_file = f'data/templates/{user_row["user_stage"].lower().replace(" ", "_")}.json'
                print(f"DEBUG: Looking for template file: {template_file} for user {user_row['first_name']} (no behavior data)")
                if os.path.exists(template_file):
                    with open(template_file, 'r') as f:
                        templates = json.load(f)
                        # Only use verified templates that exactly match the product name
                        matched_product, similarity = select_product_for_user(user_row, products)
                        
                        # Find template that matches the product
                        matching_template = None
                        for template in templates:
                            if template.get('product_name') == matched_product.get('Product_Name'):
                                matching_template = template
                                break
                        
                        if matching_template:
                            print(f"DEBUG: Found matching template for product '{matched_product.get('Product_Name')}' in stage '{user_row['user_stage']}'")
                            return {
                                'Subject': matching_template.get('subject', '').format(first_name=user_row['first_name']),
                                'Mail_Content': matching_template.get('body', '').format(first_name=user_row['first_name']),
                                'Preheader': matching_template.get('preheader', '').format(first_name=user_row['first_name']),
                                'Matched_Product': matched_product.get('Product_Name', ''),
                                'Similarity_Score': similarity,
                                'Template_Name': matching_template.get('template_name', '')
                            }
                        else:
                            print(f"DEBUG: No matching template found for product '{matched_product.get('Product_Name')}' in stage '{user_row['user_stage']}'")
                else:
                    print(f"DEBUG: Template file {template_file} does not exist")
            else:
                # User has behavior data, use AI generation with template context
                template_file = f'data/templates/{user_row["user_stage"].lower().replace(" ", "_")}.json'
                print(f"DEBUG: Looking for template file: {template_file} for user {user_row['first_name']} (with behavior data)")
                if os.path.exists(template_file):
                    with open(template_file, 'r') as f:
                        templates = json.load(f)
                        # Only use verified templates that exactly match the product name
                        matched_product, similarity = select_product_for_user(user_row, products)
                        
                        # Find template that matches the product
                        matching_template = None
                        for template in templates:
                            if template.get('product_name') == matched_product.get('Product_Name'):
                                matching_template = template
                                break
                        
                        if matching_template:
                            print(f"DEBUG: Found matching template for product '{matched_product.get('Product_Name')}' in stage '{user_row['user_stage']}'")
                            base_template = matching_template

                            settings = {
                                "tone": base_template.get('communication_settings', {}).get('tone', 'professional'),
                                "style": base_template.get('communication_settings', {}).get('style', 'formal'),
                                "length": base_template.get('communication_settings', {}).get('length', '100-150 words'),
                                "sender_name": base_template.get('communication_settings', {}).get('sender_name', 'OpenEngage Team'),
                                "template_context": {
                                    "base_template": base_template,
                                    "user_behavior": user_row['user_behaviour'] if has_behavior_data else f"Interested in {matched_product.get('Product_Name')}",
                                    "first_name": user_row['first_name'],
                                    "stage": user_row['user_stage']
                                }
                            }
                            try:
                                # Use session state crew if available, otherwise fallback to template
                                if 'st' in globals() and hasattr(st.session_state, 'crew'):
                                    response = st.session_state.crew.generate_campaign(
                                        product_data=matched_product,
                                        channels=["email"],
                                        settings=settings,
                                        stage=user_row['user_stage']
                                    )
                                else:
                                    response = None
                            except Exception as e:
                                response = None
                            if isinstance(response, dict):
                                return {
                                    'Subject': response.get('subject', ''),
                                    'Mail_Content': response.get('content', ''),
                                    'Preheader': response.get('preheader', ''),
                                    'Matched_Product': matched_product.get('Product_Name', ''),
                                    'Similarity_Score': similarity,
                                    'Template_Name': base_template.get('template_name', '')
                                }
                            else:
                                # Fallback to template if AI generation fails
                                return {
                                    'Subject': base_template.get('subject', '').format(first_name=user_row['first_name']),
                                    'Mail_Content': base_template.get('body', '').format(first_name=user_row['first_name']),
                                    'Preheader': base_template.get('preheader', '').format(first_name=user_row['first_name']),
                                    'Matched_Product': matched_product.get('Product_Name', ''),
                                    'Similarity_Score': similarity,
                                    'Template_Name': base_template.get('template_name', '')
                                }
                        else:
                            print(f"DEBUG: No matching template found for product '{matched_product.get('Product_Name')}' in stage '{user_row['user_stage']}'")
                else:
                    print(f"DEBUG: Template file {template_file} does not exist")
            
            # Fallback if no template found
            matched_product, similarity = select_product_for_user(user_row, products)
            return {
                'Subject': 'Template not found',
                'Mail_Content': 'Template not found',
                'Preheader': 'Template not found',
                'Matched_Product': matched_product.get('Product_Name', ''),
                'Similarity_Score': similarity,
                'Template_Name': 'Template not found'
            }
            
        except Exception as e:
            print(f"DEBUG: Error processing user {user_row.get('first_name', 'Unknown')}: {str(e)}")
            # Return error result
            return {
                'Subject': 'Error generating email',
                'Mail_Content': 'Error occurred during generation',
                'Preheader': 'Error',
                'Matched_Product': 'Unknown',
                'Similarity_Score': 0,
                'Template_Name': 'Error'
            }

    # Load product details
    try:
        with open('data/product_details.json', 'r') as f:
            products = json.load(f)
        
        # Filter products by organization URL if available
        if 'st' in globals() and hasattr(st.session_state, 'organization_url') and st.session_state.organization_url:
            org_url = st.session_state.organization_url
            org_products = [p for p in products if p.get('organization_url', '') == org_url]
            if org_products:
                products = org_products
        
    except Exception as e:
        print("Error loading product details")
        return data_df

    if not products:
        print("No products found. Please add products first.")
        return data_df

    print(f"DEBUG: Using {len(products)} products for email generation")

    # Use batch API if enabled
    if use_batch_api:
        try:
            from core.batch_email_generator import generate_emails_batch
            return generate_emails_batch(data_df, products, progress_callback)
        except Exception as e:
            use_batch_api = False

    # If batch API is disabled or failed, use sequential processing
    if not use_batch_api:
        total_users = len(data_df)
        for idx, row in data_df.iterrows():
            result = process_single_user(row, products)
            # Store the results directly (no fallback needed since we handle it in process_single_user)
            data_df.at[idx, 'Subject'] = result['Subject'].replace("\n", "").replace("\\n", "")
            data_df.at[idx, 'Mail_Content'] = result['Mail_Content']
            data_df.at[idx, 'Preheader'] = result['Preheader'].replace("\n", "").replace("\\n", "")
            data_df.at[idx, 'Matched_Product'] = result['Matched_Product']
            data_df.at[idx, 'Similarity_Score'] = round(float(result['Similarity_Score']) * 100, 2)
            data_df.at[idx, 'Template_Name'] = result.get('Template_Name', 'Template not found')
            # Only generate HTML if we have valid content (not "Template not found")
            if result['Subject'] and result['Mail_Content'] and result['Subject'] != 'Template not found' and result['Mail_Content'] != 'Template not found':
                if progress_callback:
                    progress_callback(idx, total_users, f"Generating HTML for: {row['first_name']}")
                has_behavior_data = ("Behaviour data not found" not in row['user_behaviour']) or ("last opened an email" in row['user_behaviour'])
                if has_behavior_data:
                    matched_product, _ = select_product_for_user(row, products)
                else:
                    matched_product = next((p for p in products if p.get('Product_Name') == result['Matched_Product']), products[0])
                email_content = {
                    'subject': result['Subject'],
                    'content': result['Mail_Content'],
                    'preheader': result['Preheader']
                }
                
                # Load communication settings
                try:
                    with open('data/communication_settings.json', 'r') as f:
                        comm_settings = json.load(f)
                except:
                    comm_settings = {}

                settings = {
                    "sender_name": matched_product.get('Company_Name', 'OpenEngage Team'),
                    "style": comm_settings.get("style", "friendly"),
                    "length": comm_settings.get("length", "100-150 words"),
                    "utm_source": "mass_email",
                    "utm_medium": "email",
                    "utm_campaign": "personalized_campaign",
                    "utm_content": row['user_stage'].lower().replace(" ", "_"),
                    "brand_personality": comm_settings.get("brand_personality", "Professional, Helpful, Trustworthy"),
                    "tone_of_voice": comm_settings.get("tone_of_voice", "Professional, Informative"),
                    "organization_url": getattr(st.session_state, 'organization_url', '') if 'st' in globals() else '',  # Add organization URL directly to settings
                    "template_context": {
                        "base_template": {
                            "product_data": matched_product
                        }
                    }
                }

                # Generate HTML content
                html_content = text_to_html(
                    email_content,
                    product_url=matched_product.get('Product_URL', ''),
                    product_name=matched_product.get('Product_Name', ''),
                    communication_settings=settings,
                    recipient_email=row['user_email'],
                    recipient_first_name=row['first_name'],
                    template_name=result.get('Template_Name', '')
                )

                # Add HTML content to the DataFrame
                data_df.at[idx, 'HTML_Content'] = html_content
        if progress_callback:
            progress_callback(total_users, total_users, "Saving HTML emails")
        os.makedirs("data/html_emails", exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        for idx, row in data_df[:5].iterrows():
            if 'HTML_Content' in row and row['HTML_Content'] and isinstance(row['HTML_Content'], str):
                try:
                    email_filename = f"data/html_emails/mass_email_{timestamp}_{idx}.html"
                    with open(email_filename, 'w') as f:
                        f.write(row['HTML_Content'])
                except Exception as e:
                    print(f"Error saving HTML file for user {row.get('first_name', idx)}: {str(e)}")
            elif 'HTML_Content' in row and row['HTML_Content'] is not None and not isinstance(row['HTML_Content'], str):
                try:
                    email_filename = f"data/html_emails/mass_email_{timestamp}_{idx}.html"
                    with open(email_filename, 'w') as f:
                        f.write(str(row['HTML_Content']))
                except Exception as e:
                    print(f"Error saving HTML file for user {row.get('first_name', idx)}: {str(e)}")

        # Generate popup data for all users
        popup_data = {
            'user_email': [],
            'behaviour_data': [],
            'target_product': [],
            'user_stage': [],
            'template_name': [],
            'popup_content': []
        }

        # Load popup template
        popup_template = """
        Generate a personalized popup content for the following user:

        User: {first_name}
        Behavior: {user_behavior}
        Target Product: {target_product}
        Current Stage: {user_stage}

        Create engaging popup content that encourages the user to take the next step in their journey.
        """

        if progress_callback:
            progress_callback(total_users, total_users, "Generating personalized popup content")

        # Collect data from all processed users and generate popup content
        for idx, row in data_df.iterrows():
            user_email = row['user_email']
            behaviour_data = row.get('user_behaviour', '')
            target_product = row.get('Matched_Product', '')
            user_stage = row.get('user_stage', '')
            first_name = row.get('first_name', user_email.split('@')[0])

            # Add base data
            popup_data['user_email'].append(user_email)
            popup_data['behaviour_data'].append(behaviour_data)
            popup_data['target_product'].append(target_product)
            popup_data['user_stage'].append(user_stage)
            popup_data['template_name'].append(row.get('Template_Name', 'None'))

            # Use LLM or existing templates for generation
            if 'st' in globals() and hasattr(st.session_state, 'crew'):
                # Format the template with user data
                popup_prompt = popup_template.format(
                    first_name=first_name,
                    user_behavior=behaviour_data,
                    target_product=target_product,
                    user_stage=user_stage
                )

                try:
                    # Call LLM for content generation using the formatted prompt
                    response = st.session_state.crew.generate_popup_content(popup_prompt)
                    popup_content = response if isinstance(response, str) else str(response)
                except Exception as e:
                    popup_content = f"Personalized content for {first_name} regarding {target_product}"
            else:
                # Fallback popup content
                popup_content = f"Hi {first_name}! We noticed you're interested in {target_product}. Take the next step in your journey!"

            popup_data['popup_content'].append(popup_content)

        # Save popup data to CSV
        popup_df = pd.DataFrame(popup_data)
        os.makedirs("Sample Data For Mass Generation", exist_ok=True)
        popup_df.to_csv("Sample Data For Mass Generation/user_popup_data.csv", index=False)

        if progress_callback:
            progress_callback(total_users, total_users, "Mass email generation completed")

    return data_df

def main(data_df=None, use_batch_api=False, organization_url=None):
    """Main function to process mass email generation"""

    # Use provided DataFrame or load from file
    if data_df is None:
        try:
            # Try to load organization-specific data first
            csv_file = 'Sample Data For Mass Generation/processed_user_data.csv'

            if not os.path.exists(csv_file):
                print(f"File not found: {csv_file}")
                return None

            print(f"Loading user data from: {csv_file}")
            data_df = pd.read_csv(csv_file)

            # Limit to first 10 users for testing
            data_df = data_df.head(10)

        except Exception as e:
            print(f"Error loading data: {e}")
            return None

    print(f"Loaded {len(data_df)} users for email generation")

    try:
        # Process emails
        def progress_callback(current, total, message):
            print(f"Progress: {current}/{total} - {message}")

        result_df = process_mass_email_data(data_df, progress_callback, use_batch_api)

        # Save results
        output_file = f'Sample Data For Mass Generation/mass_email_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        result_df.to_csv(output_file, index=False)

        print(f"Results saved to: {output_file}")
        print("Sample results:")
        if 'Subject' in result_df.columns and 'Matched_Product' in result_df.columns:
            print(result_df[['user_email', 'first_name', 'Subject', 'Matched_Product']].head())
        else:
            print(result_df.head())

        return {
            "total_emails": len(result_df),
            "results_file": output_file,
            "html_emails_folder": "data/html_emails/",
            "popup_data_file": "Sample Data For Mass Generation/user_popup_data.csv",
            "organization_url": organization_url or ""
        }

    except Exception as e:
        print(f"Error in main: {e}")
        return None

if __name__ == "__main__":
    result = main()
