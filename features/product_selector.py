"""
Product Selection Backend Script
Aggregates all product selection functionality exactly from the original scripts.

This script combines functionality from:
- src/openengage/core/product_selector.py
- src/openengage/core/journey_builder.py (find_similar_product function)

Inputs:
- user_data: Dictionary containing user information with user_stage, user_behaviour, last_product_sent, Cross_Sell flags
- products: List of product dictionaries with Product_Name, Company_Name, Type_of_Product, Product_Summary, Product_Features, Priority
- activity: User activity/behavior description for similarity matching

Outputs:
- Selected product dictionary and similarity/confidence score
- Product matching results with detailed scoring
"""
import re

# Import find_similar_product function from journey_builder
# Note: In backend usage, this function is included below to avoid import dependencies
# Cache for storing embeddings to avoid redundant API calls
_embedding_cache = {}

def select_product_for_user(user_data, products):
    """
    Select the most appropriate product for a user based on their stage and behavior.

    Args:
        user_data (dict): User data containing 'user_stage' and 'user_behaviour'
        products (list): List of product dictionaries

    Returns:
        tuple: (selected_product, similarity_score)
    """
    # Extract user stage and behavior
    user_stage = user_data.get('user_stage', '')
    user_behavior = user_data.get('user_behaviour', '')
    last_product_sent = user_data.get('last_product_sent', '')

    # NEW: Check for Cross_Sell flag and Cross_Sell_Product
    if 'Cross_Sell' in user_data and user_data['Cross_Sell'] == True and 'Cross_Sell_Product' in user_data and user_data['Cross_Sell_Product']:
        # User is eligible for cross-sell, use the designated cross-sell product
        cross_sell_product_name = user_data['Cross_Sell_Product']

        # Find the cross-sell product in the products list
        for product in products:
            if product['Product_Name'] == cross_sell_product_name:
                # Return the cross-sell product with highest confidence score
                return product, 1.0

    # Check if user has behavior data
    has_behavior_data = ("Behaviour data not found" not in user_behavior)
    print(has_behavior_data)

    if last_product_sent:
        for i in products:
            if i['Product_Name'] == last_product_sent:
                return i, 1.0
    # If no behavior data, randomly assign a product
    if not has_behavior_data:
        return select_by_priority(products), 0.0

    # Case 1: New Visitor - Use similarity logic
    if user_stage == 'New Visitor':
        return find_similar_product(user_behavior, products)

    # Case 2: Product Page Viewed or Product Lead Generated
    elif user_stage in ['Product Page Viewed']:
        # Try to extract product information from user behavior
        viewed_products = extract_viewed_products(user_behavior, products)

        # If we found products the user has interacted with
        if viewed_products:
            # If user has interacted with only one product, return that
            if len(viewed_products) == 1:
                return viewed_products[0], 1.0

            # If user has interacted with multiple products, use priority to decide
            elif len(viewed_products) > 1:
                return select_by_priority(viewed_products), 1.0

        # If no product information found in behavior, fall back to similarity
        return find_similar_product(user_behavior, products)

    # Case 2: Product Page Viewed or Product Lead Generated
    elif user_stage in ['Product Lead Generated']:
        # Try to extract product information from user behavior
        lead_products = extract_lead_products(user_behavior, products)

        # If we found products the user has interacted with
        if lead_products:
            # If user has interacted with only one product, return that
            if len(lead_products) == 1:
                return lead_products[0], 1.0

            # If user has interacted with multiple products, use priority to decide
            elif len(lead_products) > 1:
                return select_by_priority(lead_products), 1.0

        # If no product information found in behavior, fall back to similarity
        return find_similar_product(user_behavior, products)

    # Default case: Use similarity logic
    else:
        return find_similar_product(user_behavior, products)


def extract_viewed_products(user_behavior, products):
    """
    Extract products that the user has viewed or interacted with based on behavior description.

    Args:
        user_behavior (str): Description of user behavior
        products (list): List of product dictionaries

    Returns:
        list: List of products the user has interacted with
    """

    product_names = re.findall(r'visited the product pages for ([^.]+)', user_behavior)
    visited_products=[i for i in products if i['Product_Name'] in product_names]

    return visited_products

def extract_lead_products(user_behavior, products):
    """
    Extract products that the user has shown interest in based on behavior description.

    Args:
        user_behavior (str): Description of user behavior
        products (list): List of product dictionaries
    Returns:
        list: List of products the user has shown interest in
    """

    product_names = re.findall(r'shown interest in ([^.]+)', user_behavior)
    lead_products=[i for i in products if i['Product_Name'] in product_names]
    return lead_products


def select_by_priority(products):
    """
    Select product with highest priority (lowest priority number).

    Args:
        products (list): List of product dictionaries

    Returns:
        dict: Product with highest priority
    """
    # Sort products by priority (lower number = higher priority)
    sorted_products = sorted(products, key=lambda p: p.get('Priority', float('inf')))

    # Return the highest priority product
    return sorted_products[0]


def find_similar_product(activity, products):
    """Find most similar product based on activity description using OpenAI embeddings"""
    import os
    import hashlib
    from openai import OpenAI

    # Initialize OpenAI client
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    # Function to get embeddings for a text with caching
    def get_embedding(text):
        if not text or text.strip() == "":
            text = "No description available"

        # Create a hash of the text to use as cache key
        text_hash = hashlib.md5(text.encode()).hexdigest()

        # Check if we have this embedding cached
        global _embedding_cache
        if text_hash in _embedding_cache:
            return _embedding_cache[text_hash]

        try:
            response = client.embeddings.create(
                model="text-embedding-3-small",
                input=text,
                encoding_format="float"
            )
            embedding = response.data[0].embedding

            # Cache the result
            _embedding_cache[text_hash] = embedding
            return embedding
        except Exception as e:
            print(f"Error getting embedding: {str(e)}")
            # Return a zero vector as fallback
            return [0.0] * 1536  # Default dimension for text-embedding-3-small

    # Function to calculate cosine similarity between two vectors
    def cosine_similarity(vec1, vec2):
        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        norm_a = sum(a * a for a in vec1) ** 0.5
        norm_b = sum(b * b for b in vec2) ** 0.5

        if norm_a == 0 or norm_b == 0:
            return 0.0

        return dot_product / (norm_a * norm_b)

    # Get embedding for the activity
    activity_embedding = get_embedding(activity)

    # Get embeddings for each product summary
    product_embeddings = []
    product_texts = []

    print(f"\n===== SEMANTIC PRODUCT MATCHING =====\n")
    print(f"User activity/behavior: {activity[:100]}..." if len(activity) > 100 else f"User activity/behavior: {activity}")
    print(f"Number of products to compare: {len(products)}")

    for i, product in enumerate(products):
        # Combine product details for better context
        product_name = product.get('Product_Name', 'Unknown Product')
        company_name = product.get('Company_Name', 'Unknown Company')
        product_type = product.get('Type_of_Product', '')
        product_summary = product.get('Product_Summary', '')

        # Build a rich context for the product
        product_text = f"Product: {product_name}\n"
        product_text += f"Company: {company_name}\n"
        if product_type:
            product_text += f"Type: {product_type}\n"
        product_text += f"Summary: {product_summary}\n"

        # Add features if available
        if 'Product_Features' in product and product['Product_Features']:
            product_text += "Features:\n"
            for feature in product['Product_Features']:
                product_text += f"- {feature}\n"

        print(f"\nProduct {i+1}: {product_name}")
        print(f"Text length: {len(product_text)} characters")

        product_texts.append(product_text)
        product_embeddings.append(get_embedding(product_text))

    # Calculate similarities
    similarities = [cosine_similarity(activity_embedding, prod_emb) for prod_emb in product_embeddings]

    # Get most similar product
    if not similarities or len(similarities) == 0:
        print("\nNo similarities calculated. Returning default product.")
        return products[0] if products else None, 0.0

    # Create a list of (index, similarity) tuples and sort by similarity
    similarity_pairs = [(i, sim) for i, sim in enumerate(similarities)]
    similarity_pairs.sort(key=lambda x: x[1], reverse=True)

    # Get the most similar product
    most_similar_idx = similarity_pairs[0][0]
    similarity_score = similarity_pairs[0][1]

    # Print detailed results
    print("\n----- Similarity Results -----")
    for i, (idx, score) in enumerate(similarity_pairs):
        product_name = products[idx].get('Product_Name', 'Unknown')
        print(f"{i+1}. {product_name}: {score:.4f}" + (" (BEST MATCH)" if idx == most_similar_idx else ""))

    print(f"\nBest match: {products[most_similar_idx].get('Product_Name', 'Unknown')}")
    print(f"Similarity score: {similarity_score:.4f}")
    print("\n==============================\n")

    return products[most_similar_idx], similarity_score


def find_similar_product(activity, products):
    """Find most similar product based on activity description using OpenAI embeddings"""
    
    # Initialize OpenAI client
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    # Function to get embeddings for a text with caching
    def get_embedding(text):
        if not text or text.strip() == "":
            text = "No description available"

        # Create a hash of the text to use as cache key
        text_hash = hashlib.md5(text.encode()).hexdigest()

        # Check if we have this embedding cached
        global _embedding_cache
        if text_hash in _embedding_cache:
            return _embedding_cache[text_hash]

        try:
            response = client.embeddings.create(
                model="text-embedding-3-small",
                input=text,
                encoding_format="float"
            )
            embedding = response.data[0].embedding

            # Cache the result
            _embedding_cache[text_hash] = embedding
            return embedding
        except Exception as e:
            print(f"Error getting embedding: {str(e)}")
            # Return a zero vector as fallback
            return [0.0] * 1536  # Default dimension for text-embedding-3-small

    # Function to calculate cosine similarity between two vectors
    def cosine_similarity(vec1, vec2):
        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        norm_a = sum(a * a for a in vec1) ** 0.5
        norm_b = sum(b * b for b in vec2) ** 0.5

        if norm_a == 0 or norm_b == 0:
            return 0.0

        return dot_product / (norm_a * norm_b)

    # Get embedding for the activity
    activity_embedding = get_embedding(activity)

    # Get embeddings for each product summary
    product_embeddings = []
    product_texts = []

    print(f"\n===== SEMANTIC PRODUCT MATCHING =====\n")
    print(f"User activity/behavior: {activity[:100]}..." if len(activity) > 100 else f"User activity/behavior: {activity}")
    print(f"Number of products to compare: {len(products)}")

    for i, product in enumerate(products):
        # Combine product details for better context
        product_name = product.get('Product_Name', 'Unknown Product')
        company_name = product.get('Company_Name', 'Unknown Company')
        product_type = product.get('Type_of_Product', '')
        product_summary = product.get('Product_Summary', '')

        # Build a rich context for the product
        product_text = f"Product: {product_name}\n"
        product_text += f"Company: {company_name}\n"
        if product_type:
            product_text += f"Type: {product_type}\n"
        product_text += f"Summary: {product_summary}\n"

        # Add features if available
        if 'Product_Features' in product and product['Product_Features']:
            product_text += "Features:\n"
            for feature in product['Product_Features']:
                product_text += f"- {feature}\n"

        print(f"\nProduct {i+1}: {product_name}")
        print(f"Text length: {len(product_text)} characters")

        product_texts.append(product_text)
        product_embeddings.append(get_embedding(product_text))

    # Calculate similarities
    similarities = [cosine_similarity(activity_embedding, prod_emb) for prod_emb in product_embeddings]

    # Get most similar product
    if not similarities or len(similarities) == 0:
        print("\nNo similarities calculated. Returning default product.")
        return products[0] if products else None, 0.0

    # Create a list of (index, similarity) tuples and sort by similarity
    similarity_pairs = [(i, sim) for i, sim in enumerate(similarities)]
    similarity_pairs.sort(key=lambda x: x[1], reverse=True)

    # Get the most similar product
    most_similar_idx = similarity_pairs[0][0]
    similarity_score = similarity_pairs[0][1]

    # Print detailed results
    print("\n----- Similarity Results -----")
    for i, (idx, score) in enumerate(similarity_pairs):
        product_name = products[idx].get('Product_Name', 'Unknown')
        print(f"{i+1}. {product_name}: {score:.4f}" + (" (BEST MATCH)" if idx == most_similar_idx else ""))

    print(f"\nBest match: {products[most_similar_idx].get('Product_Name', 'Unknown')}")
    print(f"Similarity score: {similarity_score:.4f}")
    print("\n==============================\n")

    return products[most_similar_idx], similarity_score
