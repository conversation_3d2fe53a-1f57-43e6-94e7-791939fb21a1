from flask import Flask
from config import config


def create_app(config_name='production'):

    app = Flask(__name__)
    app.config.from_object(config['production'])

    # Set API_TOKEN for authentication
    app.config['API_TOKEN'] = app.config.get('TOKEN', 'default-token-replace-in-production')

    from core.api import api_bp
    app.register_blueprint(api_bp, url_prefix='/api')

    return app
