"""
Synthetic User Data Generation Backend Script
Aggregates all user data generation functionality exactly from the original scripts.

This script combines functionality from:
- new_data.py (main user data generation functions)
- src/openengage/core/user_data_processor.py (user behavior generation)

Inputs:
- company_name: Name of the company/organization
- num_users: Number of users to generate (default: 200)
- organization_url: URL of the organization (optional)

Outputs:
- CSV file with generated user data saved to Sample Data For Mass Generation folder
- Returns DataFrame with generated user data
"""

import pandas as pd
import json
import random
import asyncio
import os
import sys
from datetime import datetime, timedelta
from faker import Faker
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize faker
fake = Faker()

def load_product_details():
    """Load product details from JSON file"""
    try:
        with open('data/product_details.json', 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading product details: {e}")
        return []

def get_logged_in_user_organization():
    """Get the organization of the currently logged-in user from users.json"""
    try:
        # First try to get organization URL from command line (passed from UI)
        import sys
        if len(sys.argv) > 1:
            org_url = sys.argv[1]

            # Load users.json to find the user with matching organization URL
            with open('data/users.json', 'r') as f:
                users = json.load(f)

            for user in users:
                if user.get('organization', {}).get('url') == org_url:
                    org_data = user['organization']
                    return {
                        "company_name": org_data['name'],
                        "url": org_data['url']
                    }

        # Fallback: try to get from session state or find a default user
        # For now, return the first user's organization as fallback
        with open('data/users.json', 'r') as f:
            users = json.load(f)

        if users:
            first_user = users[0]
            org_data = first_user.get('organization', {})
            return {
                "company_name": org_data.get('name', 'Default Organization'),
                "url": org_data.get('url', '')
            }
        else:
            return {
                "company_name": "Default Organization",
                "url": ""
            }

    except Exception as e:
        print(f"Error getting organization data: {e}")
        return {
            "company_name": "Default Organization",
            "url": ""
        }

def generate_random_datetime_last_week():
    """Generate random datetime from last week (without minutes/seconds)"""
    today = datetime.now()
    last_week_start = today - timedelta(days=7)
    last_week_end = today - timedelta(days=1)
    
    # Random day between last week start and end
    random_days = random.randint(0, 6)
    random_date = last_week_start + timedelta(days=random_days)
    
    # Random hour (0-23)
    random_hour = random.randint(0, 23)
    
    # Set minutes and seconds to 0
    return random_date.replace(hour=random_hour, minute=0, second=0, microsecond=0)

def generate_send_time(open_time):
    """Generate send time (day-1 from open_time or random if open_time is NaN)"""
    if pd.isna(open_time):
        return generate_random_datetime_last_week()
    else:
        # Day before open_time
        return open_time - timedelta(days=1)

def generate_user_data(company_name, num_users=200):
    """Generate user data for a specific company"""

    # Load product details
    products = load_product_details()

    # Filter products for the company using organization URL matching
    org_data = get_logged_in_user_organization()
    company_products = []

    # Filter by organization URL if available
    if org_data.get('url'):
        company_products = [p for p in products if p.get('organization_url', '') == org_data['url']]

    # Fallback to all products if no organization-specific products found
    if not company_products:
        company_products = products

    # Load user journey data to get product-specific stages
    user_journey_data = {}
    try:
        with open('data/user_journey.json', 'r') as f:
            user_journey_data = json.load(f)
    except Exception as e:
        print(f"Warning: Could not load user_journey.json: {e}")

    # Lists for randomization
    countries = [
        "United States", "India", "United Kingdom", "Canada", "Australia",
        "Germany", "France", "Japan", "Brazil", "Mexico", "South Africa",
        "Singapore", "Netherlands", "Sweden", "Norway", "Denmark",
        "Switzerland", "Italy", "Spain", "South Korea"
    ]

    content_mediums = ["video", "blog"]

    job_roles = [
        "Data Scientist", "Software Engineer", "Product Manager", "Marketing Manager",
        "Business Analyst", "DevOps Engineer", "UX Designer", "Sales Manager",
        "Project Manager", "Research Scientist", "Machine Learning Engineer",
        "Frontend Developer", "Backend Developer", "Full Stack Developer",
        "Data Engineer", "AI Researcher", "Digital Marketing Specialist",
        "Content Creator", "Growth Hacker", "Technical Writer", "QA Engineer",
        "System Administrator", "Cloud Architect", "Cybersecurity Analyst",
        "Business Intelligence Analyst"
    ]

    # Engagement statuses
    engagement_statuses = ["High", "Medium", "Low", "Inactive"]

    users_data = []
    used_emails = set()  # Track used emails for uniqueness

    for i in range(num_users):
        # Generate basic user info
        first_name = fake.first_name()

        # Generate unique email
        email = f"{first_name.lower()}@{company_name.lower().replace(' ', '')}.com"
        counter = 1
        while email in used_emails:
            email = f"{first_name.lower()}{counter}@{company_name.lower().replace(' ', '')}.com"
            counter += 1
        used_emails.add(email)

        # Random selections
        country = random.choice(countries)
        content_medium = random.choice(content_mediums)
        job_role = random.choice(job_roles)
        engagement_status = random.choice(engagement_statuses)

        # Handle empty product list
        if company_products:
            selected_product = random.choice(company_products)
            last_product_sent = selected_product['Product_Name']
        else:
            # Create a default product if none available
            selected_product = {
                'Product_Name': 'Default Product',
                'Company_Name': company_name,
                'Type_of_Product': 'General',
                'Product_Summary': 'Default product for testing',
                'Product_Features': 'Basic features',
                'Priority': 1
            }
            last_product_sent = selected_product['Product_Name']

        # Get stages for the selected product from user_journey.json
        product_stages = user_journey_data.get(last_product_sent, user_journey_data.get('default', []))

        if product_stages:
            # Sort stages by s_no to maintain priority order
            sorted_stages = sorted(product_stages, key=lambda x: x.get('s_no', 0))
            # Exclude the last stage (highest s_no)
            if len(sorted_stages) > 1:
                available_stages = sorted_stages[:-1]
                selected_stage = random.choice(available_stages)
                user_stage = selected_stage['current_stage']
            else:
                user_stage = sorted_stages[0]['current_stage'] if sorted_stages else "New Visitor"
        else:
            # Fallback to default stages if no product-specific stages found
            user_stage = "New Visitor"
        
        # Generate random times with more diverse logic
        # 60% chance of having open/click times, 40% NaN for more diversity
        if random.random() < 0.6:
            last_open_time = generate_random_datetime_last_week()
            # 70% chance of having click time if open time exists (reduced from 80%)
            if random.random() < 0.7:
                # Click time should be after open time with more varied delays
                delay_hours = random.choice([0, 1, 2, 3, 6, 12, 24, 48])  # More varied delays
                last_click_time = last_open_time + timedelta(hours=delay_hours)
            else:
                last_click_time = None
        else:
            last_open_time = None
            last_click_time = None
        
        send_time = generate_send_time(last_open_time)
        
        user_data = {
            'user_email': email,
            'first_name': first_name,
            'country': country,
            'content_medium': content_medium,
            'job_role': job_role,
            'user_stage': user_stage,
            'engagement_status': engagement_status,
            'last_product_sent': last_product_sent,
            'Last_Open_Time': last_open_time.strftime('%Y-%m-%d %H:%M:%S') if last_open_time else None,
            'Last_Click_Time': last_click_time.strftime('%Y-%m-%d %H:%M:%S') if last_click_time else None,
            'Send_Time': send_time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        users_data.append(user_data)
    
    return users_data

async def generate_content_titles(company_products, llm):
    """Generate content titles based on company products"""
    try:
        # Create product details string
        product_details = []
        for product in company_products:
            product_info = f"- {product.get('Product_Name', 'Unknown')}: {product.get('Product_Summary', 'No description')}"
            product_details.append(product_info)

        products_text = "\n".join(product_details)

        # Create prompt for content generation
        content_prompt = f"""
        Based on these company products:
        {products_text}

        Generate exactly 50 content titles:
        - 30 blog post titles
        - 20 video content titles

        Make them relevant to the products and engaging for the target audience.
        Format as JSON with two arrays: "blog_titles" and "video_titles"
        """

        # Generate content using LLM
        response = await llm.ainvoke(content_prompt)

        try:
            # Parse JSON response
            import json
            content_data = json.loads(response.content)
            blog_titles = content_data.get('blog_titles', [])
            video_titles = content_data.get('video_titles', [])

            # Ensure we have the right number of titles
            if len(blog_titles) < 30:
                blog_titles.extend([f"Blog: Understanding {product['Product_Name']}" for product in company_products[:30-len(blog_titles)]])
            if len(video_titles) < 20:
                video_titles.extend([f"Video: {product['Product_Name']} Tutorial" for product in company_products[:20-len(video_titles)]])

            return blog_titles[:30], video_titles[:20]

        except json.JSONDecodeError:
            # Fallback to default titles
            blog_titles = [f"Understanding {product['Product_Name']}: A Complete Guide" for product in company_products[:30]]
            video_titles = [f"{product['Product_Name']}: Video Tutorial" for product in company_products[:20]]
            return blog_titles, video_titles

    except Exception as e:
        print(f"Error generating content titles: {e}")
        # Fallback to default titles
        blog_titles = [f"Blog: {product['Product_Name']} Guide" for product in company_products[:30]]
        video_titles = [f"Video: {product['Product_Name']} Overview" for product in company_products[:20]]
        return blog_titles, video_titles

async def generate_user_behavior(users_data, company_products):
    """Generate user behavior content using GPT-4o-mini"""
    # Initialize LLM
    llm_model = "gpt-4o-mini-2024-07-18"
    api_key = os.getenv("OPENAI_API_KEY")

    if not api_key:
        print("Warning: OpenAI API key not found. Using placeholder behavior.")
        # Add placeholder behavior for each user
        for user in users_data:
            user['user_behaviour'] = f"{user['first_name']} is interested in {user['last_product_sent']} and has {user['engagement_status'].lower()} engagement with our content."
            user['content_viewed'] = "No content data available"
        return users_data

    llm = ChatOpenAI(temperature=0.9, model=llm_model, api_key=api_key)

    # Generate content titles using only company-specific products
    print("Generating content titles...")
    blog_titles, video_titles = await generate_content_titles(company_products, llm)

    # Add content_viewed to each user based on their content_medium preference
    for user in users_data:
        if user['content_medium'] == 'blog':
            # Randomly select 1-3 blog titles
            num_content = random.randint(1, 3)
            selected_content = random.sample(blog_titles, min(num_content, len(blog_titles)))
        else:  # video
            # Randomly select 1-3 video titles
            num_content = random.randint(1, 3)
            selected_content = random.sample(video_titles, min(num_content, len(video_titles)))

        user['content_viewed'] = ", ".join(selected_content)

    # Generate user behavior using chain.map().ainvoke() pattern
    print("Generating user behavior descriptions...")

    # Create prompt template
    products_prompt = """
    You are generating realistic user behavior descriptions for marketing purposes.

    User Information:
    - Name: {first_name}
    - Email: {user_email}
    - Country: {country}
    - Content Medium Preference: {content_medium}
    - Current Stage: {user_stage}
    - Engagement Level: {engagement_status}
    - Content Viewed: {content_viewed}

    Generate a realistic user behavior description (2-4 sentences) that incorporates:
    1. User's location/country and job role context
    2. Their content preference (video or blog content)
    3. The specific content they've been viewing
    4. Their engagement pattern based on the timing data
    5. You can mention things like Visited Product Page or Opened last mail, but not always

    Make it sound natural and realistic for marketing personalization.
    """

    try:
        # Create chain for batch processing
        from langchain.prompts import PromptTemplate
        prompt_template = PromptTemplate(
            input_variables=["first_name", "user_email", "country", "content_medium", "user_stage", "engagement_status", "content_viewed"],
            template=products_prompt
        )

        chain = prompt_template | llm

        # Prepare inputs for batch processing
        inputs = []
        for user in users_data:
            inputs.append({
                "first_name": user['first_name'],
                "user_email": user['user_email'],
                "country": user['country'],
                "content_medium": user['content_medium'],
                "user_stage": user['user_stage'],
                "engagement_status": user['engagement_status'],
                "content_viewed": user['content_viewed']
            })

        # Process in batches using map().ainvoke()
        print(f"Processing {len(inputs)} users for behavior generation...")
        responses = await chain.map().ainvoke(inputs)

        # Update users_data with generated behavior
        for i, response in enumerate(responses):
            if i < len(users_data):
                users_data[i]['user_behaviour'] = response.content.strip()

    except Exception as e:
        print(f"Error generating user behavior: {e}")
        # Fallback to simple behavior descriptions
        for user in users_data:
            user['user_behaviour'] = f"{user['first_name']} is interested in {user['last_product_sent']} and has {user['engagement_status'].lower()} engagement with our content."
            if 'content_viewed' not in user:
                user['content_viewed'] = "No content data available"

    return users_data

def save_company_csv(company_name, users_data):
    """Save user data to company-specific CSV file"""

    # Create directory if it doesn't exist
    os.makedirs('Sample Data For Mass Generation', exist_ok=True)

    # Create filename
    safe_company_name = company_name.replace(' ', '_').replace('/', '_')
    filename = f'Sample Data For Mass Generation/{safe_company_name}_processed_user_data.csv'

    # Convert to DataFrame
    df = pd.DataFrame(users_data)

    # Reorder columns to match the expected format
    column_order = [
        'user_email', 'first_name', 'country', 'content_medium', 'job_role',
        'content_viewed', 'user_behaviour', 'user_stage', 'engagement_status',
        'last_product_sent', 'Last_Open_Time', 'Last_Click_Time', 'Send_Time'
    ]

    # Ensure all columns exist
    for col in column_order:
        if col not in df.columns:
            df[col] = ''

    # Reorder columns
    df = df[column_order]

    # Save to CSV
    df.to_csv(filename, index=False)
    print(f"Saved {len(users_data)} users to {filename}")

    return filename

async def main(company_name=None, num_users=200, organization_url=None):
    """Main function to generate company-specific user data"""

    # Use provided parameters or get from logged-in user
    if not company_name:
        org_data = get_logged_in_user_organization()
        company_name = org_data.get('company_name', 'Default Organization')
        if not organization_url:
            organization_url = org_data.get('url')

    print(f"Generating user data for company: {company_name}")

    # Load products for the company
    products = load_product_details()

    # Filter products for the company using organization URL matching
    company_products = [p for p in products if p.get('organization_url', '').strip() != '']

    # If we have organization URL, filter by organization URL
    if organization_url:
        company_products = [p for p in products if p.get('organization_url', '') == organization_url]

    # Fallback to all products if no organization-specific products found
    if not company_products:
        company_products = products

    print(f"Found {len(company_products)} products for {company_name}")

    # Generate user data
    print("Generating basic user data...")
    users_data = generate_user_data(company_name, num_users=num_users)

    # Generate user behavior using LLM
    print("Generating user behavior descriptions...")
    users_data = await generate_user_behavior(users_data, company_products)

    # Save to CSV
    filename = save_company_csv(company_name, users_data)

    print(f"Successfully generated {filename}")
    print("Sample data:")
    df = pd.DataFrame(users_data)
    print(df.head())

    return df

if __name__ == "__main__":
    result_df = asyncio.run(main())
