#!/usr/bin/env python3
"""
Final comprehensive test of all API endpoints.
This script tests all API endpoints to ensure they're working correctly.
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:5001/api"
HEADERS = {
    "Content-Type": "application/json",
    "X-API-Token": "default-token-replace-in-production"
}

def test_endpoint(endpoint, method="POST", data=None):
    """Test a single API endpoint"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method == "POST":
            response = requests.post(url, headers=HEADERS, json=data, timeout=30)
        elif method == "GET":
            response = requests.get(url, headers=HEADERS, timeout=30)
        
        success = response.status_code == 200
        result = response.json() if response.status_code == 200 else {"error": response.text}
        
        return {
            "endpoint": endpoint,
            "status_code": response.status_code,
            "success": success,
            "result": result
        }
        
    except Exception as e:
        return {
            "endpoint": endpoint,
            "status_code": 0,
            "success": False,
            "result": {"error": str(e)}
        }

def main():
    """Test all API endpoints"""
    print("Final Comprehensive API Test")
    print("=" * 60)
    
    # All API endpoints to test
    test_cases = [
        # Existing APIs
        {"endpoint": "/organization-data", "data": {"organization_url": "https://www.analyticsvidhya.com/"}},
        {"endpoint": "/brand-archetype-analysis", "data": {"org_url": "https://www.analyticsvidhya.com/"}},
        
        # New APIs - User Data Generation
        {"endpoint": "/generate-user-data", "data": {"company_name": "Test Company", "num_users": 3, "organization_url": "https://example.com"}},
        
        # New APIs - Funnel Analysis
        {"endpoint": "/analyze-funnel", "data": {"organization_name": "Test Company", "csv_files": {}}},
        
        # New APIs - Email Generation
        {"endpoint": "/generate-mass-emails", "data": {"data_file": "test_data.csv", "use_batch_api": False, "organization_url": "https://example.com"}},
        {"endpoint": "/generate-personalized-emails", "data": {"data_file": "test_data.csv", "organization_url": "https://example.com", "personalization_level": "high"}},
        
        # New APIs - Journey Creation
        {"endpoint": "/create-journey-tree", "data": {"stages": ["Awareness", "Consideration", "Decision"], "start_idx": 0, "organization_url": "https://example.com"}},
        
        # New APIs - Analytics
        {"endpoint": "/analytics-dashboard", "data": {"data_type": "engagement", "csv_data": [{"user_email": "<EMAIL>", "user_stage": "Awareness", "engagement_score": 0.8}], "time_unit": "D", "metric_type": "open_rate"}},
        {"endpoint": "/campaign-performance-analysis", "data": {"campaign_data": [{"campaign_name": "Test Campaign", "open_rate": 0.25, "click_rate": 0.05}], "performance_metrics": ["open_rate", "click_rate"], "time_period": "last_30_days"}},
        
        # New APIs - Product Selection
        {"endpoint": "/select-products", "data": {"user_data": {"user_email": "<EMAIL>", "job_role": "Data Scientist", "content_viewed": "Machine Learning", "user_behaviour": "Active learner"}, "available_products": []}},
        
        # New APIs - Insights
        {"endpoint": "/generate-insights", "data": {"data_type": "campaign_performance", "filters": {"campaign_type": "email"}, "analysis_type": "summary"}},
        {"endpoint": "/sql-query", "data": {"question": "Show me all available tables in the database"}},
    ]
    
    # Test results
    results = []
    successful_tests = 0
    total_tests = len(test_cases)
    
    print(f"Testing {total_tests} API endpoints...\n")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"[{i}/{total_tests}] Testing {test_case['endpoint']}...")
        
        result = test_endpoint(test_case["endpoint"], "POST", test_case["data"])
        results.append(result)
        
        if result["success"]:
            successful_tests += 1
            print(f"  ✅ SUCCESS")
        else:
            print(f"  ❌ FAILED - Status: {result['status_code']}")
            if result["result"].get("error"):
                error_msg = result["result"]["error"]
                if len(error_msg) > 100:
                    error_msg = error_msg[:100] + "..."
                print(f"     Error: {error_msg}")
        
        time.sleep(0.5)  # Small delay between requests
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Total Tests: {total_tests}")
    print(f"Successful: {successful_tests}")
    print(f"Failed: {total_tests - successful_tests}")
    print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
    
    # Detailed results
    print(f"\n{'='*60}")
    print("DETAILED RESULTS")
    print(f"{'='*60}")
    
    for result in results:
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        print(f"{status} {result['endpoint']} (Status: {result['status_code']})")
    
    print(f"\n{'='*60}")
    print("API TESTING COMPLETED!")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
