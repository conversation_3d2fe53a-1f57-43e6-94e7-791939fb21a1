"""
Analytics Dashboard Journey Funnel Backend Script
Aggregates all analytics dashboard functionality including engagement data processing,
journey funnel analysis, and campaign performance integration.

This script combines functionality from:
- src/openengage/analytics.py (engagement and journey functions)
- Integration with campaign performance analysis

Inputs:
- df: DataFrame containing engagement/journey data
- metrics_df: DataFrame with engagement metrics
- stage_counts: DataFrame with journey stage data
- time_unit: Time aggregation unit ('D', 'W', 'M')
- metric_type: Type of metrics to analyze

Outputs:
- Processed engagement and journey data
- Data structures for visualization (commented visualization code included)
"""
import pandas as pd
import altair as alt
from datetime import datetime

# Brand colors
PRIMARY = '#8D06FE'
SECONDARY = '#02B9C9'
ACCENT = '#02C688'
TERTIARY = '#F39C12'
SUCCESS = '#27AE60'
WARNING = '#F39C12'
DANGER = '#E74C3C'

def process_engagement_data(df):
    """Process the uploaded CSV data for engagement metrics."""
    # Identify which time columns are available in the data
    available_time_columns = []

    # Map standard column names to possible alternatives
    column_mapping = {
        'Send_Time': ['Send_Time', 'Last_Send_Time'],
        'Open_Time': ['Open_Time', 'Last_Open_Time'],
        'Click_Time': ['Click_Time', 'Last_Click_Time']
    }

    # Create a mapping of actual column names in the dataframe
    actual_columns = {}
    for standard_col, alternatives in column_mapping.items():
        for alt_col in alternatives:
            if alt_col in df.columns:
                actual_columns[standard_col] = alt_col
                available_time_columns.append(alt_col)
                break

    # Convert available time columns to datetime
    for col in available_time_columns:
        df[col] = pd.to_datetime(df[col], errors='coerce')

    # Ensure we have a Send_Time column to calculate date
    if 'Send_Time' in actual_columns:
        send_time_col = actual_columns['Send_Time']
        df['date'] = df[send_time_col].dt.date

        # Calculate daily metrics
        total_sent = df.groupby('date')['user_email'].count()

        # Calculate opens if we have an open time column
        if 'Open_Time' in actual_columns:
            open_time_col = actual_columns['Open_Time']
            total_opened = df.groupby('date')[open_time_col].count()
        else:
            # If no open time column, use zeros
            total_opened = pd.Series(0, index=total_sent.index)

        # Calculate clicks if we have a click time column
        if 'Click_Time' in actual_columns:
            click_time_col = actual_columns['Click_Time']
            total_clicked = df.groupby('date')[click_time_col].count()
        else:
            # If no click time column, use zeros
            total_clicked = pd.Series(0, index=total_sent.index)

        metrics_df = pd.DataFrame({
            'total_sent': total_sent,
            'total_opened': total_opened,
            'total_clicked': total_clicked
        }).reset_index()

        # Calculate rates
        metrics_df['open_rate'] = (metrics_df['total_opened'] / metrics_df['total_sent']) * 100
        metrics_df['click_rate'] = (metrics_df['total_clicked'] / metrics_df['total_sent']) * 100

        return metrics_df
    else:
        # If we don't have a send time column, create a minimal metrics dataframe
        # with the current date and zeros for all metrics
        import datetime
        current_date = datetime.datetime.now().date()
        metrics_df = pd.DataFrame({
            'date': [current_date],
            'total_sent': [0],
            'total_opened': [0],
            'total_clicked': [0],
            'open_rate': [0],
            'click_rate': [0]
        })

        return metrics_df

# VISUALIZATION FUNCTION: create_engagement_plot
# Input: metrics_df DataFrame with columns 'date', 'total_sent', 'total_opened', 'total_clicked', 'open_rate', 'click_rate'
# Input: time_unit string ('D', 'W', 'M') for time aggregation
# Input: metric_type string ('rates', 'sent', 'opens', 'clicks') for chart type
# Output: Interactive Altair chart visualization
# 
# Commented visualization code:
# def create_engagement_plot(metrics_df, time_unit='D', metric_type='rates'):
#     metrics_df['date'] = pd.to_datetime(metrics_df['date'])
#     if time_unit == 'W':
#         resampled_df = metrics_df.set_index('date').resample('W').sum().reset_index()
#     elif time_unit == 'M':
#         resampled_df = metrics_df.set_index('date').resample('M').sum().reset_index()
#     else:
#         resampled_df = metrics_df.copy()
#     
#     resampled_df['open_rate'] = (resampled_df['total_opened'] / resampled_df['total_sent']) * 100
#     resampled_df['click_rate'] = (resampled_df['total_clicked'] / resampled_df['total_sent']) * 100
#     resampled_df = resampled_df.sort_values('date')
#     
#     if metric_type == 'rates':
#         base = alt.Chart(resampled_df).encode(x=alt.X('date:T', title='Date'))
#         open_rate_chart = base.mark_line(point=True, color=PRIMARY).encode(y=alt.Y('open_rate:Q'))
#         click_rate_chart = base.mark_line(point=True, color=SECONDARY).encode(y=alt.Y('click_rate:Q'))
#         chart = alt.layer(open_rate_chart, click_rate_chart)
#     else:
#         metric_map = {'sent': 'total_sent', 'opens': 'total_opened', 'clicks': 'total_clicked'}
#         column = metric_map[metric_type]
#         chart = alt.Chart(resampled_df).mark_line(point=True).encode(
#             x=alt.X('date:T'), y=alt.Y(f'{column}:Q')
#         )
#     return chart

def filter_data_by_date(df, start_date=None, end_date=None):
    """Filter data based on date range."""
    if start_date:
        df = df[df['date'] >= pd.to_datetime(start_date).date()]
    if end_date:
        df = df[df['date'] <= pd.to_datetime(end_date).date()]
    return df

def process_user_journey_data(df, product_name=None, journey_stages=None):
    """Process user journey data from the uploaded CSV.

    Args:
        df (pd.DataFrame): DataFrame containing user journey data with columns:
            user_email, Send_Time/Last_Send_Time, Open_Time/Last_Open_Time, Click_Time/Last_Click_Time, user_stage
        product_name (str, optional): Filter data by product name. Defaults to None.
        journey_stages (list, optional): List of all journey stages in order. Defaults to None.

    Returns:
        pd.DataFrame: Processed DataFrame with user stage counts and percentages
    """
    # Filter by product if specified and if the column exists
    if product_name:
        # Check all possible product column names
        product_columns = ['offering', 'product', 'Matched_Product', 'last_product_sent', 'Last_Product_Sent']
        filtered = False

        for col in product_columns:
            if col in df.columns:
                filtered_df = df[df[col] == product_name]
                if not filtered_df.empty:
                    filtered = True
                    break

        if not filtered:
            print(f"Warning: No data found for product '{product_name}'. Using all data.")
            filtered_df = df
    else:
        filtered_df = df

    # Count users in each stage if user_stage column exists
    if 'user_stage' in filtered_df.columns:
        stage_counts = filtered_df['user_stage'].value_counts().reset_index()
        stage_counts.columns = ['stage', 'count']
    else:
        # If no user_stage column, create a basic stage count with default stages
        if journey_stages is None:
            journey_stages = ['New Visitor', 'Product Page Viewed', 'Product Lead Generated', 'Product Purchased']

        # Create empty DataFrame with stage column
        stage_counts = pd.DataFrame({'stage': journey_stages, 'count': 0})

    # Calculate percentages
    total_users = stage_counts['count'].sum()
    stage_counts['percentage'] = (stage_counts['count'] / total_users * 100).round(1) if total_users > 0 else 0

    # If journey_stages is provided, ensure all stages are included in the result
    if journey_stages:
        # Create a set of existing stages for quick lookup
        existing_stages = set(stage_counts['stage'])

        # Add missing stages with zero counts
        for stage in journey_stages:
            if stage not in existing_stages:
                new_row = pd.DataFrame({
                    'stage': [stage],
                    'count': [0],
                    'percentage': [0.0]
                })
                stage_counts = pd.concat([stage_counts, new_row], ignore_index=True)

        # Sort the DataFrame according to the journey stages order
        stage_order = {stage: i for i, stage in enumerate(journey_stages)}
        stage_counts['order'] = stage_counts['stage'].map(stage_order)
        stage_counts = stage_counts.sort_values('order').drop('order', axis=1).reset_index(drop=True)

    return stage_counts

def create_user_journey_funnel_chart(stage_counts, journey_stages, product_name=None):
    """Process funnel chart data for user journey stages.

    Args:
        stage_counts (pd.DataFrame): DataFrame with columns 'stage', 'count', and 'percentage'
        journey_stages (list): List of stages in the correct journey order
        product_name (str, optional): Name of the product for the title. Defaults to None.

    Returns:
        dict: Dictionary with funnel data including stages, counts, conversion rates
    """
    # Create a mapping of stages to their counts
    stage_to_count = dict(zip(stage_counts['stage'], stage_counts['count']))

    # Order the stages according to the journey flow
    ordered_stages = []
    ordered_counts = []
    original_counts = []

    # Use all journey stages, even if they don't have data
    for stage in journey_stages:
        ordered_stages.append(stage)
        # Use the count if available, otherwise 0
        count = stage_to_count.get(stage, 0)
        original_counts.append(count)

    # If no stages match, use the stages from stage_counts in their original order
    if not ordered_stages and not stage_counts.empty:
        ordered_stages = stage_counts['stage'].tolist()
        original_counts = stage_counts['count'].tolist()

    # Calculate cumulative counts (each stage includes sum of itself and all subsequent stages)
    cumulative_counts = []
    for i in range(len(original_counts)):
        cumulative_counts.append(sum(original_counts[i:]))

    ordered_counts = cumulative_counts

    # Calculate conversion rates between stages based on cumulative counts
    conversion_rates = []
    for i in range(len(cumulative_counts) - 1):
        if cumulative_counts[i] > 0:
            # Calculate conversion rate using cumulative counts
            rate = (cumulative_counts[i+1] / cumulative_counts[i]) * 100
            conversion_rates.append(f"{rate:.1f}%")
        else:
            conversion_rates.append("N/A")

    # Create custom hover text
    hover_text = []
    for i, (stage, cum_count, orig_count) in enumerate(zip(ordered_stages, cumulative_counts, original_counts)):
        text = f"<b>{stage}</b><br>"
        text += f"Individual Count: {orig_count}<br>"
        text += f"Cumulative Count: {cum_count}<br>"

        # Add conversion rate explanation
        if i < len(ordered_stages) - 1:
            next_cum = cumulative_counts[i+1]
            text += f"Conversion to next stage: {conversion_rates[i]}<br>"
            text += f"({next_cum}/{cum_count})"
        hover_text.append(text)

    # Set title based on product name
    title = "User Journey Funnel"
    if product_name:
        title = f"{product_name} - User Journey Funnel"

    # Return data structure for visualization
    return {
        'ordered_stages': ordered_stages,
        'ordered_counts': ordered_counts,
        'original_counts': original_counts,
        'cumulative_counts': cumulative_counts,
        'conversion_rates': conversion_rates,
        'hover_text': hover_text,
        'title': title
    }

# VISUALIZATION FUNCTION: create_user_journey_bar_chart
# Input: stage_counts DataFrame with columns 'stage', 'count', and 'percentage'
# Output: Interactive bar chart visualization
# 
# Commented visualization code:
# def create_user_journey_bar_chart(stage_counts):
#     fig = px.bar(stage_counts, x='stage', y='count', text='percentage')
#     return fig

# VISUALIZATION FUNCTION: create_user_journey_pie_chart
# Input: stage_counts DataFrame with columns 'stage', 'count', and 'percentage'
# Output: Interactive pie chart visualization
# 
# Commented visualization code:
# def create_user_journey_pie_chart(stage_counts):
#     fig = px.pie(stage_counts, values='count', names='stage', title='User Journey Stage Distribution')
#     return fig

# VISUALIZATION FUNCTION: create_campaign_prediction_visualization
# Input: prediction_data dictionary with predictions, training_metrics, campaign_name, campaign_date, campaign_size
# Output: Interactive gauge chart visualization for campaign predictions
# 
# Commented visualization code:
# def create_campaign_prediction_visualization(prediction_data):
#     predictions = prediction_data.get('predictions', {})
#     campaign_name = prediction_data.get('campaign_name', 'Unknown Campaign')
#     fig = sp.make_subplots(rows=1, cols=3, subplot_titles=("Open Rate", "Click Rate", "Unsubscribe Rate"))
#     # Add gauge indicators for each metric...
#     return fig

# Note: Only the original analytics.py functions are included above.
# Additional utility functions can be added here if needed for specific use cases.
