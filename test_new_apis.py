#!/usr/bin/env python3
"""
Test script for the new API endpoints.
This script tests all the newly added API endpoints to ensure they're working correctly.
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:5001/api"
HEADERS = {
    "Content-Type": "application/json",
    "X-API-Token": "default-token-replace-in-production"  # Default token from config
}

def test_endpoint(endpoint, method="POST", data=None):
    """Test a single API endpoint"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method == "POST":
            response = requests.post(url, headers=HEADERS, json=data, timeout=30)
        elif method == "GET":
            response = requests.get(url, headers=HEADERS, timeout=30)
        
        print(f"\n{'='*60}")
        print(f"Testing: {method} {endpoint}")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success: {result.get('success', 'N/A')}")
            print(f"Message: {result.get('message', 'N/A')}")
            if 'data' in result:
                print(f"Data keys: {list(result['data'].keys()) if isinstance(result['data'], dict) else 'Non-dict data'}")
        else:
            print(f"Error: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"\n{'='*60}")
        print(f"Testing: {method} {endpoint}")
        print(f"Connection Error: {str(e)}")
    except Exception as e:
        print(f"\n{'='*60}")
        print(f"Testing: {method} {endpoint}")
        print(f"Unexpected Error: {str(e)}")

def main():
    """Test all API endpoints including existing ones"""
    print("Testing All API Endpoints (New + Existing)")
    print("=" * 60)

    # Test data for different endpoints
    test_cases = [
        # Existing API - Brand Archetype Analysis (the one with the error)
        {
            "endpoint": "/brand-archetype-analysis",
            "method": "POST",
            "data": {
                "org_url": "https://www.analyticsvidhya.com/"
            }
        },

        # Existing API - Organization Data
        {
            "endpoint": "/organization-data",
            "method": "POST",
            "data": {
                "organization_url": "https://www.analyticsvidhya.com/"
            }
        },
        # User Data Generation
        {
            "endpoint": "/generate-user-data",
            "method": "POST",
            "data": {
                "company_name": "Test Company",
                "num_users": 5,
                "organization_url": "https://example.com"
            }
        },
        
        # Funnel Analysis
        {
            "endpoint": "/analyze-funnel",
            "method": "POST",
            "data": {
                "organization_name": "Test Company",
                "csv_files": {}
            }
        },
        
        # Mass Email Generation
        {
            "endpoint": "/generate-mass-emails",
            "method": "POST",
            "data": {
                "data_file": "test_data.csv",
                "use_batch_api": False,
                "organization_url": "https://example.com"
            }
        },
        
        # Personalized Email Generation
        {
            "endpoint": "/generate-personalized-emails",
            "method": "POST",
            "data": {
                "data_file": "test_data.csv",
                "organization_url": "https://example.com",
                "personalization_level": "high"
            }
        },
        
        # Journey Tree Creation
        {
            "endpoint": "/create-journey-tree",
            "method": "POST",
            "data": {
                "stages": ["Awareness", "Consideration", "Decision"],
                "start_idx": 0,
                "organization_url": "https://example.com"
            }
        },
        
        # Analytics Dashboard
        {
            "endpoint": "/analytics-dashboard",
            "method": "POST",
            "data": {
                "data_type": "engagement",
                "csv_data": [
                    {"user_email": "<EMAIL>", "user_stage": "Awareness", "engagement_score": 0.8}
                ],
                "time_unit": "D",
                "metric_type": "open_rate"
            }
        },
        
        # Campaign Performance Analysis
        {
            "endpoint": "/campaign-performance-analysis",
            "method": "POST",
            "data": {
                "campaign_data": [
                    {"campaign_name": "Test Campaign", "open_rate": 0.25, "click_rate": 0.05}
                ],
                "performance_metrics": ["open_rate", "click_rate"],
                "time_period": "last_30_days"
            }
        },
        
        # Product Selection
        {
            "endpoint": "/select-products",
            "method": "POST",
            "data": {
                "user_data": {
                    "user_email": "<EMAIL>",
                    "job_role": "Data Scientist",
                    "content_viewed": "Machine Learning",
                    "user_behaviour": "Active learner"
                },
                "available_products": []
            }
        },
        
        # Insights Generation
        {
            "endpoint": "/generate-insights",
            "method": "POST",
            "data": {
                "data_type": "campaign_performance",
                "filters": {"campaign_type": "email"},
                "analysis_type": "summary"
            }
        },
        
        # SQL Query
        {
            "endpoint": "/sql-query",
            "method": "POST",
            "data": {
                "question": "Show me all available tables in the database"
            }
        }
    ]
    
    # Test each endpoint
    for test_case in test_cases:
        test_endpoint(
            test_case["endpoint"],
            test_case["method"],
            test_case["data"]
        )
        time.sleep(1)  # Small delay between requests
    
    print(f"\n{'='*60}")
    print("Testing completed!")
    print("Note: Some endpoints may fail due to missing data files or dependencies.")
    print("This is expected for a basic connectivity test.")

if __name__ == "__main__":
    main()
