"""
Synthetic User Data Journey Creation Backend Script
Aggregates all journey creation functionality exactly from the original scripts.

This script combines functionality from:
- src/openengage/core/journey_builder.py (journey tree functions)
- src/openengage/core/email_generator.py (tree email generation)
- src/openengage/ui/journey.py (journey creation logic)

Inputs:
- user_data: Dictionary containing user information
- matched_product: Product data dictionary
- stages: List of journey stages
- start_idx: Starting stage index
- generation_mode: "template" or "personalized"

Outputs:
- Generated journey tree with email content for all nodes
- Node email files saved to data/node_emails folder
- Returns journey visualization data
"""

import plotly.graph_objects as go
import pandas as pd
import json
import os
import sys
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
from langchain_openai import ChatOpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def load_node_email(node_id):
    """Load email content for a specific node"""
    try:
        node_file = f'data/node_emails/{node_id}.json'
        if os.path.exists(node_file):
            with open(node_file, 'r') as f:
                return json.load(f)
        return None
    except Exception as e:
        print(f"Error loading node email {node_id}: {e}")
        return None

def save_node_email(node_id, email_data):
    """Save email content for a specific node"""
    try:
        os.makedirs('data/node_emails', exist_ok=True)
        node_file = f'data/node_emails/{node_id}.json'
        
        with open(node_file, 'w') as f:
            json.dump(email_data, f, indent=2)
        
        print(f"Saved email for {node_id}")
        return True
    except Exception as e:
        print(f"Error saving node email {node_id}: {e}")
        return False

def log_node_click(node_id, session_id):
    """Log node click event"""
    try:
        os.makedirs('data/logs', exist_ok=True)
        log_file = 'data/logs/click_logs.json'
        
        click_data = {
            'node_id': node_id,
            'session_id': session_id,
            'timestamp': datetime.now().isoformat()
        }
        
        # Load existing logs
        logs = []
        if os.path.exists(log_file):
            with open(log_file, 'r') as f:
                logs = json.load(f)
        
        # Add new log
        logs.append(click_data)
        
        # Save updated logs
        with open(log_file, 'w') as f:
            json.dump(logs, f, indent=2)
        
    except Exception as e:
        print(f"Error logging node click: {e}")

def create_journey_tree_plotly(stages, start_idx, highlighted_node=None):
    """Create an interactive journey tree using Plotly"""
    # Fixed node positions for perfect binary tree with depth=2
    node_positions = {
        1: (0, 3),    # Root
        2: (-1, 2),   # Left child of root
        3: (1, 2),    # Right child of root
        4: (-1.5, 1), # Left child of node 2
        5: (-0.5, 1), # Right child of node 2
        6: (0.5, 1),  # Left child of node 3
        7: (1.5, 1)   # Right child of node 3
    }

    # If no node is highlighted, highlight root node
    if highlighted_node is None:
        highlighted_node = 1

    # Determine stages for each node
    if start_idx < len(stages):
        current_stage = stages[start_idx]
        next_stage = stages[start_idx + 1] if start_idx + 1 < len(stages) else None
        final_stage = stages[start_idx + 2] if start_idx + 2 < len(stages) else next_stage
    else:
        current_stage = stages[0] if stages else "Unknown"
        next_stage = stages[1] if len(stages) > 1 else None
        final_stage = stages[2] if len(stages) > 2 else next_stage

    # Create node mapping with stages and colors
    node_mapping = {
        1: (current_stage, "#2ECC71", "Current Stage: User is here"),  # Root - Green
        2: (current_stage, "#E74C3C", "Not Achieved: User opened/clicked but hasn't reached goal"),  # Left - Red
        3: (next_stage, "#F1C40F", "Next Stage: User achieved the goal"),     # Right - Yellow
        4: (current_stage, "#E74C3C", "Not Achieved: User opened/clicked but hasn't reached goal"),
        5: (next_stage, "#F1C40F", "Next Stage: User achieved the goal"),
        6: (next_stage, "#E74C3C", "Not Achieved: User opened/clicked but hasn't reached goal"),
        7: (final_stage, "#F1C40F", "Next Stage: User achieved the goal")
    }

    # Create edges for the tree structure
    edges = [
        (1, 2), (1, 3),  # Root to level 1
        (2, 4), (2, 5),  # Level 1 to level 2
        (3, 6), (3, 7)
    ]

    # Create edge trace
    edge_x, edge_y = [], []
    for edge in edges:
        x0, y0 = node_positions[edge[0]]
        x1, y1 = node_positions[edge[1]]
        edge_x.extend([x0, x1, None])
        edge_y.extend([y0, y1, None])

    edge_trace = go.Scatter(
        x=edge_x, y=edge_y,
        line=dict(width=2, color='#888'),
        hoverinfo='none',
        mode='lines'
    )

    # Create node trace
    node_x, node_y, node_colors, hover_text = [], [], [], []
    node_ids = []

    for node_num in range(1, 8):
        if node_num in node_mapping and node_mapping[node_num][0]:
            stage, color, hover_info = node_mapping[node_num]
            x, y = node_positions[node_num]
            node_x.append(x)
            node_y.append(y)

            # Highlight selected node with a thicker border
            if node_num == highlighted_node:
                line_width = 4
                line_color = '#000'
            else:
                line_width = 2
                line_color = 'white'

            node_colors.append(color)
            hover_text.append(f"{hover_info}<br>Stage: {stage}")
            node_ids.append(f"node_{node_num}")

    node_trace = go.Scatter(
        x=node_x, y=node_y,
        mode='markers+text',
        text=[''] * len(node_x),
        textposition="middle center",
        hoverinfo='text',
        hovertext=hover_text,
        customdata=node_ids,
        marker=dict(
            showscale=False,
            color=node_colors,
            size=40,
            line=dict(width=line_width, color=line_color)
        )
    )

    # Add stage labels
    annotations = []
    for node_num in range(1, 8):
        if node_num in node_mapping and node_mapping[node_num][0]:
            stage, _, _ = node_mapping[node_num]
            x, y = node_positions[node_num]
            annotations.append(
                dict(
                    x=x,
                    y=y - 0.3,
                    text=stage,
                    showarrow=False,
                    font=dict(size=10),
                    xanchor='center'
                )
            )

    fig = go.Figure(
        data=[edge_trace, node_trace],
        layout=go.Layout(
            showlegend=False,
            hovermode='closest',
            margin=dict(b=20, l=20, r=20, t=20),
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False, range=[-2, 2]),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False, range=[0, 4]),
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            width=600,
            height=500,
            clickmode='event',
            annotations=annotations
        )
    )

    return fig, node_ids

def create_user_journey_flow(journey_steps):
    """Create a visual flow of the user's actual journey"""
    if not journey_steps:
        return None

    # Extract data for visualization
    stages = [step['stage'] for step in journey_steps]
    timestamps = [step['timestamp'] for step in journey_steps]
    actions = [step['action'] for step in journey_steps]

    # Create positions for nodes
    x_positions = list(range(len(stages)))
    y_positions = [1] * len(stages)

    # Create edges between consecutive nodes
    edge_x, edge_y = [], []
    for i in range(len(x_positions) - 1):
        edge_x.extend([x_positions[i], x_positions[i + 1], None])
        edge_y.extend([y_positions[i], y_positions[i + 1], None])

    # Edge trace
    edge_trace = go.Scatter(
        x=edge_x, y=edge_y,
        line=dict(width=3, color='#1f77b4'),
        hoverinfo='none',
        mode='lines'
    )

    # Node trace
    node_trace = go.Scatter(
        x=x_positions, y=y_positions,
        mode='markers+text',
        text=stages,
        textposition="top center",
        hoverinfo='text',
        hovertext=[f"Stage: {stage}<br>Action: {action}<br>Time: {timestamp}" 
                  for stage, action, timestamp in zip(stages, actions, timestamps)],
        marker=dict(
            size=30,
            color='#1f77b4',
            line=dict(width=2, color='white')
        )
    )

    # Timestamp trace (below nodes)
    timestamp_trace = go.Scatter(
        x=x_positions, y=[0.7] * len(x_positions),
        mode='text',
        text=timestamps,
        textposition="middle center",
        hoverinfo='none',
        textfont=dict(size=10, color='gray')
    )

    # Create figure
    fig = go.Figure(
        data=[edge_trace, node_trace, timestamp_trace],
        layout=go.Layout(
            showlegend=False,
            hovermode='closest',
            margin=dict(b=50, l=20, r=20, t=20),
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False, range=[0.5, 2]),
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)'
        )
    )

    return fig

def load_template_for_stage(stage):
    """Load email template for a specific stage"""
    try:
        # Convert stage to filename format
        stage_filename = stage.lower().replace(' ', '_').replace('-', '_')
        template_path = f'data/templates/{stage_filename}.json'
        
        if os.path.exists(template_path):
            with open(template_path, 'r') as f:
                return json.load(f)
        
        # Fallback to a default template structure
        return {
            "template_name": f"{stage} Template",
            "subject": f"Regarding {stage}",
            "body": f"Hello {{first_name}}, we noticed you're in the {stage} stage.",
            "communication_settings": {
                "tone": "professional",
                "style": "formal",
                "length": "100-150 words",
                "sender_name": "OpenEngage Team"
            }
        }
        
    except Exception as e:
        print(f"Error loading template for stage {stage}: {e}")
        return None

async def generate_node_email(stage, matched_product, user_data, is_followup=False, parent_email=None, generation_mode="template"):
    """Generate email for a node"""
    try:
        # Initialize settings with defaults
        settings = {
            "tone": "professional",
            "style": "formal",
            "length": "100-150 words",
            "sender_name": "OpenEngage Team",
            "template_context": {
                "user_behavior": user_data.get('user_behaviour', 'No behavior data'),
                "first_name": user_data.get('first_name', 'User'),
                "stage": stage
            }
        }

        if is_followup and parent_email:
            # Generate follow-up email
            email_content = {
                'subject': f"Follow-up: {parent_email.get('subject', 'Your Journey')}",
                'content': f"Hi {user_data.get('first_name', 'there')}, we wanted to follow up on your interest in {matched_product.get('Product_Name', 'our product')}.",
                'preheader': f"Following up on {stage}"
            }
        else:
            # Load template for the stage
            template = load_template_for_stage(stage)
            
            if template and generation_mode == "template":
                # Use template-based generation
                email_content = {
                    'subject': template.get('subject', '').format(first_name=user_data.get('first_name', 'User')),
                    'content': template.get('body', '').format(first_name=user_data.get('first_name', 'User')),
                    'preheader': f"Personalized message for {user_data.get('first_name', 'User')}"
                }
            else:
                # Use LLM generation
                llm_model = "gpt-4o-mini-2024-07-18"
                api_key = os.getenv("OPENAI_API_KEY")
                
                if api_key:
                    llm = ChatOpenAI(temperature=0.7, model=llm_model, api_key=api_key)
                    
                    prompt = f"""
                    Generate a personalized email for:
                    User: {user_data.get('first_name', 'User')} in {stage} stage
                    Behavior: {user_data.get('user_behaviour', 'No behavior data')}
                    Product: {matched_product.get('Product_Name', 'Unknown')}
                    
                    Generate JSON with subject, content, and preheader.
                    """
                    
                    response = await llm.ainvoke(prompt)
                    
                    try:
                        import json
                        email_content = json.loads(response.content)
                    except:
                        email_content = {
                            'subject': f"Your {stage} Journey",
                            'content': f"Hi {user_data.get('first_name', 'there')}, we're here to help with {matched_product.get('Product_Name', 'our product')}.",
                            'preheader': f"Personalized for {stage}"
                        }
                else:
                    # Fallback without API
                    email_content = {
                        'subject': f"Your {stage} Journey",
                        'content': f"Hi {user_data.get('first_name', 'there')}, we're here to help with {matched_product.get('Product_Name', 'our product')}.",
                        'preheader': f"Personalized for {stage}"
                    }

        # Create complete email object
        email_data = {
            'subject': email_content.get('subject', ''),
            'content': email_content.get('content', ''),
            'preheader': email_content.get('preheader', ''),
            'stage': stage,
            'product': matched_product.get('Product_Name', ''),
            'user_name': user_data.get('first_name', 'User'),
            'generation_mode': generation_mode,
            'timestamp': datetime.now().isoformat(),
            'settings': settings
        }

        return email_data

    except Exception as e:
        print(f"Error generating email for stage {stage}: {e}")
        return None

async def generate_tree_emails(stage, level, position, matched_product, user_data, start_idx, stages, max_level=2, generation_mode="template"):
    """Generate emails for all nodes in the tree"""

    # Calculate node number (1-7) from level and position
    node_number = 2**level + position
    node_id = f'node_{node_number}'

    print(f"Generating email for {node_id} at level {level}, position {position}")

    try:
        if level == 0:
            # Root node - generate initial email
            email = await generate_node_email(stage, matched_product, user_data, False, None, generation_mode)

            if email:
                save_node_email(node_id, email)
                print(f"Successfully saved email for {node_id}")
            else:
                print(f"Failed to generate email for {node_id}")
        else:
            # Child node - determine if it's a follow-up or next stage
            parent_number = node_number // 2
            parent_id = f'node_{parent_number}'
            is_right = node_number % 2 == 1

            # Load parent email
            parent_email = load_node_email(parent_id)

            if is_right:
                # Right child - next stage progression
                next_stage_idx = start_idx + level
                if next_stage_idx < len(stages):
                    next_stage = stages[next_stage_idx]
                    email = await generate_node_email(next_stage, matched_product, user_data, False, parent_email, generation_mode)
                else:
                    # No more stages, generate completion email
                    email = await generate_node_email("Journey Complete", matched_product, user_data, False, parent_email, generation_mode)
            else:
                # Left child - follow-up in same stage
                email = await generate_node_email(stage, matched_product, user_data, True, parent_email, generation_mode)

            if email:
                save_node_email(node_id, email)
                print(f"Successfully saved email for {node_id}")
            else:
                print(f"Failed to generate email for {node_id}")

        # Generate children if not at max level
        if level < max_level:
            # Determine stages for children
            if level == 0:
                left_child_stage = stage  # Follow-up in same stage
                right_child_stage = stages[start_idx + 1] if start_idx + 1 < len(stages) else None
            else:
                left_child_stage = stage  # Follow-up
                right_child_stage = stages[start_idx + level + 1] if start_idx + level + 1 < len(stages) else None

            # Generate left child (follow-up path)
            await generate_tree_emails(left_child_stage, level + 1, position * 2, matched_product, user_data, start_idx, stages, max_level, generation_mode)

            # Generate right child (next stage path)
            if right_child_stage:
                await generate_tree_emails(right_child_stage, level + 1, position * 2 + 1, matched_product, user_data, start_idx, stages, max_level, generation_mode)

        print(f"Completed email generation for {node_id}")

    except Exception as e:
        print(f"Error in generate_tree_emails for {node_id}: {e}")
        return None

def find_similar_product(activity, products):
    """Find most similar product based on activity description"""
    try:
        # Simple keyword matching - can be enhanced with embeddings
        activity_lower = activity.lower()

        best_match = None
        best_score = 0

        for product in products:
            score = 0
            product_name = product.get('Product_Name', '').lower()
            product_summary = product.get('Product_Summary', '').lower()

            # Check for keyword matches
            if product_name in activity_lower:
                score += 2
            if any(word in activity_lower for word in product_name.split()):
                score += 1
            if any(word in activity_lower for word in product_summary.split()):
                score += 0.5

            if score > best_score:
                best_score = score
                best_match = product

        return best_match if best_match else (products[0] if products else None)

    except Exception as e:
        print(f"Error finding similar product: {e}")
        return products[0] if products else None

async def create_user_journey(user_data, stages, start_idx=0, generation_mode="template"):
    """Create a complete user journey with email tree"""

    print(f"Creating journey for user: {user_data.get('first_name', 'Unknown')}")

    # Load products
    try:
        with open('data/product_details.json', 'r') as f:
            products = json.load(f)
    except Exception as e:
        print(f"Error loading products: {e}")
        products = []

    # Find matching product
    user_behavior = user_data.get('user_behaviour', '')
    matched_product = find_similar_product(user_behavior, products)

    if not matched_product:
        matched_product = {
            'Product_Name': 'Default Product',
            'Company_Name': 'OpenEngage',
            'Product_Summary': 'Our flagship product',
            'Product_Features': ['Feature 1', 'Feature 2']
        }

    print(f"Matched product: {matched_product.get('Product_Name', 'Unknown')}")

    # Generate journey tree emails
    current_stage = stages[start_idx] if start_idx < len(stages) else stages[0]

    print("Generating journey tree emails...")
    await generate_tree_emails(current_stage, 0, 0, matched_product, user_data, start_idx, stages, max_level=2, generation_mode=generation_mode)

    # Create journey visualization
    fig, node_ids = create_journey_tree_plotly(stages, start_idx, 1)

    # Create journey steps for flow visualization
    journey_steps = [
        {
            'stage': current_stage,
            'action': 'Journey Started',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M')
        }
    ]

    # Add subsequent stages
    for i in range(start_idx + 1, min(start_idx + 3, len(stages))):
        journey_steps.append({
            'stage': stages[i],
            'action': 'Stage Progression',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M')
        })

    journey_flow_fig = create_user_journey_flow(journey_steps)

    print("Journey creation completed successfully!")

    return {
        'user_data': user_data,
        'matched_product': matched_product,
        'stages': stages,
        'start_idx': start_idx,
        'journey_tree_figure': fig,
        'journey_flow_figure': journey_flow_fig,
        'journey_steps': journey_steps,
        'node_ids': node_ids,
        'generation_mode': generation_mode
    }

async def main():
    """Main function to create user journey"""

    # Sample user data (you can modify this or load from file)
    user_data = {
        'first_name': 'John',
        'user_email': '<EMAIL>',
        'user_behaviour': 'Interested in data science courses and has viewed multiple product pages',
        'user_stage': 'Product Page Viewed',
        'country': 'United States',
        'job_role': 'Data Scientist'
    }

    # Sample stages (you can load from user_journey.json)
    stages = ['New Visitor', 'Product Page Viewed', 'Product Lead Generated', 'Product Purchased']

    print("Creating synthetic user journey...")

    # Create journey
    journey_result = await create_user_journey(user_data, stages, start_idx=1, generation_mode="template")

    if journey_result:
        print("Journey created successfully!")
        print(f"User: {journey_result['user_data']['first_name']}")
        print(f"Product: {journey_result['matched_product']['Product_Name']}")
        print(f"Stages: {journey_result['stages']}")
        print(f"Journey steps: {len(journey_result['journey_steps'])}")

        # Save journey tree figure
        if journey_result['journey_tree_figure']:
            journey_result['journey_tree_figure'].write_html("journey_tree.html")
            print("Journey tree saved as journey_tree.html")

        # Save journey flow figure
        if journey_result['journey_flow_figure']:
            journey_result['journey_flow_figure'].write_html("journey_flow.html")
            print("Journey flow saved as journey_flow.html")

        return journey_result
    else:
        print("Failed to create journey")
        return None

if __name__ == "__main__":
    result = asyncio.run(main())
