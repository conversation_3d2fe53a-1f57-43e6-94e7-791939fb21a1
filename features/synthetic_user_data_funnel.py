"""
Synthetic User Data Funnel Backend Script
Aggregates all funnel data processing functionality exactly from the original scripts.

This script combines functionality from:
- src/openengage/ui/funnel_view.py (funnel data loading and processing)
- backend/analytics_dashboard_journey_funnel.py (funnel analysis functions)

Inputs:
- csv_files_dict: Dictionary mapping stage names to CSV file paths
- funnel_data: Dictionary mapping stage names to DataFrames
- organization_name: Name of the organization (optional)

Outputs:
- Plotly funnel visualization figure
- Processed funnel data with stage counts and percentages
- Returns funnel analysis results
"""

import pandas as pd
import json
import os
import plotly.graph_objects as go
from datetime import datetime
from typing import Dict, List, Optional, Any

def get_organization_name_from_session():
    """Get organization name from session or users.json"""
    try:
        # Try to get from command line argument first
        import sys
        if len(sys.argv) > 1:
            org_url = sys.argv[1]
            
            # Load users.json to find the user with matching organization URL
            with open('data/users.json', 'r') as f:
                users = json.load(f)
            
            for user in users:
                if user.get('organization', {}).get('url') == org_url:
                    return user['organization']['name']
        
        # Fallback to first user's organization
        with open('data/users.json', 'r') as f:
            users = json.load(f)
        
        if users:
            return users[0].get('organization', {}).get('name', 'Analytics Vidhya')
        
        return 'Analytics Vidhya'
        
    except Exception as e:
        print(f"Error getting organization name: {e}")
        return 'Analytics Vidhya'

def detect_organization_csv_files():
    """
    Detect organization-specific CSV files for funnel analysis.
    
    Returns:
        tuple: (organization_name, csv_files_dict, fallback_used)
    """
    org_name = get_organization_name_from_session()
    
    # Check for organization-specific CSV file
    safe_org_name = org_name.replace(' ', '_').replace('/', '_')
    org_csv_file = f'Sample Data For Mass Generation/{safe_org_name}_processed_user_data.csv'
    
    funnel_files = {}
    fallback_used = False
    
    if os.path.exists(org_csv_file):
        # Use organization-specific file
        funnel_files['processed_user_data'] = org_csv_file
    else:
        # Fallback to default file
        default_file = 'Sample Data For Mass Generation/processed_user_data.csv'
        if os.path.exists(default_file):
            funnel_files['processed_user_data'] = default_file
            fallback_used = True
        else:
            # Check for any available CSV files in the directory
            csv_dir = 'Sample Data For Mass Generation'
            if os.path.exists(csv_dir):
                csv_files = [f for f in os.listdir(csv_dir) if f.endswith('_processed_user_data.csv')]
                if csv_files:
                    # Use the first available processed user data file
                    default_file = os.path.join(csv_dir, csv_files[0])
                    funnel_files['processed_user_data'] = default_file
                    fallback_used = True

    return org_name, funnel_files, fallback_used

def load_funnel_data(csv_files_dict):
    """
    Load and process funnel data from CSV files.

    Args:
        csv_files_dict: Dictionary mapping stage names to CSV file paths

    Returns:
        dict: Dictionary mapping stage names to DataFrames
    """
    funnel_data = {}

    for stage_name, file_path in csv_files_dict.items():
        try:
            if os.path.exists(file_path):
                # Check if this is a processed_user_data file or funnel file
                if 'processed_user_data' in stage_name:
                    # For processed user data files, use different columns
                    use_cols = ['user_email', 'first_name', 'user_behaviour', 'user_stage']

                    # Load CSV with specific columns for performance
                    df = pd.read_csv(file_path, usecols=lambda x: x in use_cols)

                    # Group by user_stage to create funnel stages
                    if 'user_stage' in df.columns and not df.empty:
                        # Filter out rows with missing essential data
                        df = df.dropna(subset=['user_email', 'first_name'])
                        df = df[df['user_stage'].notna()]

                        # Group by stage and create separate DataFrames
                        for stage, stage_df in df.groupby('user_stage'):
                            if not stage_df.empty:
                                funnel_data[stage] = stage_df.reset_index(drop=True)
                    else:
                        # If no user_stage column, treat as single stage
                        df = df.dropna(subset=['user_email', 'first_name'])
                        if not df.empty:
                            funnel_data['All_Users'] = df
                else:
                    # For funnel files, use original columns
                    use_cols = ['user_email', 'first_name', 'user_behaviour', 'user_stage',
                                'Subject', 'Mail_Content', 'Matched_Product', 'Template_Name']

                    # Load CSV with specific columns for performance
                    df = pd.read_csv(file_path, usecols=lambda x: x in use_cols)

                    # Filter out rows with missing essential data
                    df = df.dropna(subset=['user_email', 'first_name'])

                    if not df.empty:
                        funnel_data[stage_name] = df

        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            continue

    return funnel_data

def create_funnel_visualization(funnel_data):
    """
    Create a funnel visualization showing user progression through stages.
    Uses user_journey.json to determine stage order by s_no and implements
    proper funnel logic where each stage shows total minus previous stages.

    Args:
        funnel_data: Dictionary mapping stage names to DataFrames

    Returns:
        plotly.graph_objects.Figure: Funnel chart
    """
    if not funnel_data:
        return None

    # Get the actual stage names from the data
    data_stage_names = set(funnel_data.keys()) - {'campaigns', 'All_Users'}

    if not data_stage_names:
        return None

    # Load user journey configuration to get stage order
    try:
        with open('data/user_journey.json', 'r') as f:
            user_journey_data = json.load(f)

        # Find the best matching configuration based on stage names
        best_match_config = None
        max_matches = 0

        for config_name, config_stages in user_journey_data.items():
            if config_name == 'default':
                continue
            
            config_stage_names = {stage['current_stage'] for stage in config_stages}
            matches = len(data_stage_names.intersection(config_stage_names))
            
            if matches > max_matches:
                max_matches = matches
                best_match_config = config_stages

        # If no good match found, try organization-specific or default
        if not best_match_config:
            org_name = get_organization_name_from_session()
            best_match_config = user_journey_data.get(org_name, user_journey_data.get('default', []))

        # Sort stages by s_no to get correct funnel order
        journey_stages = sorted(best_match_config, key=lambda x: x.get('s_no', 0))

        # Extract stage names in s_no order
        stage_order = [stage['current_stage'] for stage in journey_stages]

    except Exception as e:
        # Fallback: sort data stages alphabetically
        stage_order = sorted(list(data_stage_names))

    # Filter stage_order to only include stages that exist in funnel_data
    available_stages = []
    for stage in stage_order:
        if stage in funnel_data:
            available_stages.append(stage)

    # Add any remaining stages that weren't in the journey config
    for stage in data_stage_names:
        if stage not in available_stages:
            available_stages.append(stage)

    if not available_stages:
        return None

    # Count users in each stage
    stage_user_counts = {}
    for stage_name in available_stages:
        if stage_name in funnel_data:
            stage_user_counts[stage_name] = len(funnel_data[stage_name])

    # Calculate funnel counts using proper funnel logic
    # Stage 1 (s_no 1): sum of all stages (total users)
    # Stage 2 (s_no 2): sum of stage 2 onwards (total - stage 1 users)
    # Stage 3 (s_no 3): sum of stage 3 onwards (total - stage 1 - stage 2 users)
    stage_counts = {}
    stage_names = []

    for i, stage_name in enumerate(available_stages):
        # Calculate cumulative count: sum from current stage to end
        # This creates the funnel effect where each stage shows remaining users
        cumulative_count = sum(stage_user_counts.get(s, 0) for s in available_stages[i:])

        stage_counts[stage_name] = cumulative_count
        stage_names.append(stage_name.replace('_', ' ').title())

    # Create funnel chart
    fig = go.Figure()

    # Plotly funnel displays from top to bottom, so we need the data in the correct order
    # The first stage (s_no 1) should be at the top with the highest count
    fig.add_trace(go.Funnel(
        y=stage_names,
        x=list(stage_counts.values()),
        textinfo="value+percent initial",
        textfont=dict(size=14),
        marker=dict(
            color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98FB98'],
            line=dict(width=2, color='white')
        ),
        connector=dict(line=dict(color='rgb(63, 63, 63)', dash='dot', width=3))
    ))

    # Update layout
    fig.update_layout(
        title={
            'text': 'User Journey Funnel',
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 20}
        },
        font=dict(size=12),
        margin=dict(l=20, r=20, t=60, b=20),
        height=400
    )

    return fig

def process_user_journey_data(df, product_name=None, journey_stages=None):
    """Process user journey data from the uploaded CSV.

    Args:
        df (pd.DataFrame): DataFrame containing user journey data with columns:
            user_email, Send_Time/Last_Send_Time, Open_Time/Last_Open_Time, Click_Time/Last_Click_Time, user_stage
        product_name (str, optional): Filter data by product name. Defaults to None.
        journey_stages (list, optional): List of all journey stages in order. Defaults to None.

    Returns:
        pd.DataFrame: DataFrame with stage counts and percentages
    """
    # Make a copy to avoid modifying the original
    filtered_df = df.copy()

    # Filter by product if specified
    if product_name and 'last_product_sent' in filtered_df.columns:
        filtered_df = filtered_df[filtered_df['last_product_sent'] == product_name]
    elif product_name and 'Matched_Product' in filtered_df.columns:
        filtered_df = filtered_df[filtered_df['Matched_Product'] == product_name]

    # Count users in each stage if user_stage column exists
    if 'user_stage' in filtered_df.columns:
        stage_counts = filtered_df['user_stage'].value_counts().reset_index()
        stage_counts.columns = ['stage', 'count']
    else:
        # If no user_stage column, create a basic stage count with default stages
        if journey_stages is None:
            journey_stages = ['New Visitor', 'Product Page Viewed', 'Product Lead Generated', 'Product Purchased']

        # Create empty DataFrame with stage column
        stage_counts = pd.DataFrame({'stage': journey_stages, 'count': 0})

    # Calculate percentages
    total_users = stage_counts['count'].sum()
    stage_counts['percentage'] = (stage_counts['count'] / total_users * 100).round(1) if total_users > 0 else 0

    return stage_counts

def main(organization_name=None, csv_files=None):
    """Main function to process funnel data and create visualization"""

    print("Processing synthetic user data funnel...")

    # Use provided parameters or detect organization CSV files
    if organization_name and csv_files:
        org_name = organization_name
        fallback_used = False
    else:
        org_name, csv_files, fallback_used = detect_organization_csv_files()

    if fallback_used:
        print(f"Warning: No organization-specific data found for '{org_name}'. Using default funnel data.")
    else:
        print(f"Using organization-specific funnel data for: {org_name}")

    # Load funnel data
    funnel_data = load_funnel_data(csv_files)
    
    if not funnel_data:
        print("No funnel data found.")
        return None
    
    print(f"Loaded funnel data with {len(funnel_data)} stages:")
    for stage, df in funnel_data.items():
        print(f"  - {stage}: {len(df)} users")
    
    # Create funnel visualization
    fig = create_funnel_visualization(funnel_data)
    
    if fig:
        print("Funnel visualization created successfully.")
        # Save the figure as HTML for viewing
        fig.write_html("funnel_visualization.html")
        print("Funnel visualization saved as funnel_visualization.html")
    else:
        print("Failed to create funnel visualization.")
    
    # Process journey data for the first available stage
    if funnel_data:
        first_stage_data = list(funnel_data.values())[0]
        journey_analysis = process_user_journey_data(first_stage_data)
        print("\nJourney Analysis:")
        print(journey_analysis)
    
    return {
        'organization_name': org_name,
        'funnel_data': funnel_data,
        'funnel_figure': fig,
        'fallback_used': fallback_used
    }

if __name__ == "__main__":
    result = main()
