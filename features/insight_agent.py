import os
import sqlite3
import json
import yaml
from pydantic import BaseModel
from dotenv import load_dotenv
from typing import Annotated, Literal, Any, Dict, List, Optional, Union
from langchain_openai import ChatOpenAI
from langchain_core.tools import tool
from langchain_core.prompts import ChatPromptTemplate
from langchain_community.utilities import SQLDatabase 
from langchain_core.messages import AIMessage, AnyMessage, ToolMessage, HumanMessage
from langchain_core.runnables import RunnableLambda, RunnableWithFallbacks
from langchain_community.agent_toolkits import SQLDatabaseToolkit
from pydantic import BaseModel as PydanticBaseModel, Field
from typing_extensions import TypedDict
from langgraph.graph import END, StateGraph, START
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode

# Request model
class QueryRequest(BaseModel):
    question: str

# Response model
class QueryResponse(BaseModel):
    result: str
    rows: List[List[Any]]
    columns: List[str]

# Load environment variables
load_dotenv()

# Configuration
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DB_NAME = os.path.join(root_dir, "email_marketing.db")

# Load prompts and SQL queries
def load_config():
    config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config')
    prompts_path = os.path.join(config_dir, 'prompts.yml')
    queries_path = os.path.join(config_dir, 'sql_queries.yml')

    # Default prompts if config file not found
    default_prompts = {
        'query_check_system': "You are a SQL expert. Check and validate SQL queries for correctness.",
        'query_generation_system': "You are a SQL expert. Generate SQL queries based on natural language requests."
    }

    try:
        with open(prompts_path, 'r', encoding='utf-8') as file:
            prompts_config = yaml.safe_load(file)
            prompts = prompts_config.get('sql_agent', default_prompts)
    except FileNotFoundError:
        print(f"Warning: Prompts config file not found at: {prompts_path}. Using defaults.")
        prompts = default_prompts

    try:
        with open(queries_path, 'r', encoding='utf-8') as file:
            queries_config = yaml.safe_load(file)
    except FileNotFoundError:
        print(f"Warning: SQL queries config file not found at: {queries_path}. Using empty config.")
        queries_config = {}

    return prompts, queries_config

try:
    prompts, sql_queries = load_config()
except Exception as e:
    print(f"Warning: Config loading failed: {e}. Using minimal defaults.")
    prompts = {
        'query_check_system': "You are a SQL expert. Check and validate SQL queries for correctness.",
        'query_generation_system': "You are a SQL expert. Generate SQL queries based on natural language requests."
    }
    sql_queries = {}

# Database initialization
if not os.path.exists(DB_NAME):
    print(f"Warning: Database file {DB_NAME} not found. Creating empty database.")
    # Create empty database
    try:
        connection = sqlite3.connect(DB_NAME, timeout=60.0)
        connection.execute('PRAGMA journal_mode=DELETE')
        connection.execute('PRAGMA synchronous=NORMAL')
        connection.execute('PRAGMA busy_timeout=60000')
        # Create a simple table for testing
        connection.execute('''
            CREATE TABLE IF NOT EXISTS query_cache (
                nlp_query TEXT PRIMARY KEY,
                sql_query TEXT
            )
        ''')
        connection.commit()
        connection.close()
        print("Empty database created successfully.")
    except sqlite3.Error as e:
        print(f"Warning: SQLite connection error: {e}")

try:
    connection = sqlite3.connect(DB_NAME, timeout=60.0)
    connection.execute('PRAGMA journal_mode=DELETE')
    connection.execute('PRAGMA synchronous=NORMAL')
    connection.execute('PRAGMA busy_timeout=60000')
    connection.close()
except sqlite3.Error as e:
    print(f"Warning: SQLite connection error: {e}")

# LLM initialization
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise Exception("OPENAI_API_KEY not set")
os.environ["OPENAI_API_KEY"] = OPENAI_API_KEY
llm = ChatOpenAI(model="gpt-4o")

# SQLDatabase setup
try:
    uri = f"sqlite:///{DB_NAME}"
    db = SQLDatabase.from_uri(uri)
except Exception as e:
    print(f"Warning: Database connection error: {str(e)}. Using minimal setup.")
    db = None

# Global variables for query results
last_query_rows = []
last_query_columns = []

# Cache functions
def get_cached_sql(user_input: str) -> Optional[str]:
    try:
        conn = sqlite3.connect(DB_NAME)
        cursor = conn.cursor()
        cursor.execute("SELECT sql_query FROM query_cache WHERE nlp_query = ?", (user_input,))
        row = cursor.fetchone()
        conn.close()
        return row[0] if row else None
    except Exception as e:
        return None

def save_to_cache(user_input: str, generated_sql: str):
    try:
        conn = sqlite3.connect(DB_NAME)
        cursor = conn.cursor()
        cursor.execute("INSERT OR REPLACE INTO query_cache (nlp_query, sql_query) VALUES (?, ?)",
                      (user_input, generated_sql))
        conn.commit()
        conn.close()
    except Exception:
        pass

# Tools setup
if db is not None:
    toolkit = SQLDatabaseToolkit(db=db, llm=llm)
    sql_tools = toolkit.get_tools()
    list_tables_tool = next((t for t in sql_tools if t.name == "sql_db_list_tables"), None)
    get_schema_tool = next((t for t in sql_tools if t.name == "sql_db_schema"), None)
else:
    toolkit = None
    sql_tools = []
    list_tables_tool = None
    get_schema_tool = None

@tool
def db_query_tool(query: str) -> str:
    """Execute SQL query on the database and return results."""
    global last_query_rows, last_query_columns
    try:
        with sqlite3.connect(DB_NAME, timeout=60.0) as conn:
            conn.execute('PRAGMA busy_timeout=60000')
            cursor = conn.cursor()
            cursor.execute(query)
            rows = cursor.fetchall()
            rows = [list(row) for row in rows]
            column_names = [description[0] for description in cursor.description] if cursor.description else []
            
            last_query_rows = rows
            last_query_columns = column_names
            
            if not rows:
                return "Query executed successfully, but no results were found for your criteria."
            
            result = "\n" + " | ".join(column_names) + "\n"
            result += "-" * len(result) + "\n"
            for row in rows[:10]:
                result += " | ".join(str(cell) for cell in row) + "\n"
            return result
    except Exception as e:
        return f"Error: {str(e)}"

class SubmitFinalAnswer(PydanticBaseModel):
    final_answer: str = Field(..., description="The final natural language answer to the user's question.")

# State definition
class State(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]
    error: str | None
    original_query: str | None
    cached_sql: str | None

# Prompt setup
query_check_system = prompts['query_check_system']
query_check_prompt = ChatPromptTemplate.from_messages([
    ("system", query_check_system),
    ("placeholder", "{messages}")
])
query_check = query_check_prompt | llm.bind_tools([db_query_tool])

query_gen_system = prompts['query_generation_system']
query_gen_prompt = ChatPromptTemplate.from_messages([
    ("system", query_gen_system),
    ("placeholder", "{messages}")
])
query_gen = query_gen_prompt | llm.bind_tools([SubmitFinalAnswer])

# Graph nodes
def first_tool_call(state: State) -> dict[str, list[AIMessage]]:
    return {"messages": [AIMessage(content="", tool_calls=[{"name": "sql_db_list_tables", "args": {}, "id": "tool_list_tables"}])]}

def handle_tool_error(state: State) -> dict:
    error = state.get("error")
    messages = state.get("messages", [])
    if not messages or not hasattr(messages[-1], 'tool_calls') or not messages[-1].tool_calls:
        return {"messages": [AIMessage(content=f"An unexpected error occurred: {repr(error)}. Please try rephrasing your request.")]}
    
    tool_calls = messages[-1].tool_calls
    return {
        "messages": [
            ToolMessage(
                content=f"Error: Tool call failed with error: {repr(error)}. Please analyze the error and fix your approach.",
                tool_call_id=tc["id"],
            )
            for tc in tool_calls
        ]
    }

def create_tool_node_with_fallback(tools: list) -> RunnableWithFallbacks[Any, dict]:
    return ToolNode(tools).with_fallbacks([RunnableLambda(handle_tool_error)], exception_key="error")

def cache_check_node(state: State):
    user_query = next((msg.content for msg in state["messages"] if isinstance(msg, HumanMessage)), None)
    if user_query:
        cached_sql = get_cached_sql(user_query)
        if cached_sql:
            return {
                "original_query": user_query,
                "cached_sql": cached_sql,
                "messages": [AIMessage(content=cached_sql)]
            }
    return {
        "original_query": user_query,
        "cached_sql": None
    }

def query_gen_node(state: State):
    response_message = query_gen.invoke({"messages": state["messages"]})
    
    if response_message.content and "sender_info = " in response_message.content.lower():
        response_message.content = response_message.content.replace(
            "sender_info = ", "sender_info LIKE '%"
        ).replace("'", "%'", 1)

    tool_error_messages = []
    if response_message.tool_calls:
        for tc in response_message.tool_calls:
            if tc["name"] != "SubmitFinalAnswer":
                tool_error_messages.append(
                    ToolMessage(
                        content=f"Error: You called the wrong tool: {tc['name']}. Please regenerate the SQL query as plain text.",
                        tool_call_id=tc["id"],
                    )
                )

    return {"messages": [response_message] + tool_error_messages}

def model_check_query_node(state: State) -> dict[str, list[AIMessage]]:
    if state.get("cached_sql"):
        return {
            "messages": [AIMessage(
                content="",
                tool_calls=[{
                    "name": "db_query_tool",
                    "args": {"query": state["cached_sql"]},
                    "id": "cached_query_call"
                }]
            )]
        }
    
    if state["messages"][-1].tool_calls:
        return {"messages": [AIMessage(content="Graph Logic Error: model_check_query_node received unexpected tool call.")]}
    
    raw_sql_query_message = HumanMessage(content=state["messages"][-1].content)
    checked_query_response = query_check.invoke({"messages": [raw_sql_query_message]})
    
    if state.get("original_query") and hasattr(checked_query_response, 'tool_calls') and checked_query_response.tool_calls:
        for tc in checked_query_response.tool_calls:
            if tc["name"] == "db_query_tool":
                generated_sql = tc.get("args", {}).get("query")
                if generated_sql:
                    save_to_cache(state["original_query"], generated_sql)
                    break
    
    return {"messages": [checked_query_response]}

def should_continue(state: State) -> Literal[END, "model_check_query_node", "query_gen_node"]:
    last_message = state["messages"][-1]
    
    if isinstance(last_message, ToolMessage) and "Error: Tool call failed" in last_message.content:
        return "query_gen_node"
    
    if isinstance(last_message, ToolMessage) and "Query executed successfully, but no results were found" in last_message.content:
        state["messages"].append(AIMessage(
            content="No records found in the database.",
            tool_calls=[{"name": "SubmitFinalAnswer", "args": {"final_answer": "No records found in the database."}, "id": "final_answer_no_results"}]
        ))
        return END
    
    if getattr(last_message, "tool_calls", None):
        if any(tc["name"] == "SubmitFinalAnswer" for tc in last_message.tool_calls):
            return END
        return "query_gen_node"
    
    if isinstance(last_message, AIMessage) and last_message.content:
        content_lower = last_message.content.lower()
        if state.get("cached_sql") or any(keyword in content_lower for keyword in ["select ", "insert ", "update ", "delete ", "create ", "drop ", "alter "]):
            return "model_check_query_node"
        
        if any(keyword in content_lower for keyword in ["about", "describe", "tell me about", "database"]):
            schema_description = last_message.content
            state["messages"].append(AIMessage(
                content=schema_description,
                tool_calls=[{"name": "SubmitFinalAnswer", "args": {"final_answer": schema_description}, "id": "final_answer_describe_db"}]
            ))
            return END
        
        return "query_gen_node"
    
    return "query_gen_node"

model_get_schema_prompt = ChatPromptTemplate.from_messages([
    ("system", "Based on the following list of tables, identify all relevant tables for answering typical email marketing analysis questions. Then, call the 'sql_db_schema' tool with a comma-separated list of these table names to get their schema. Tables: {table_list}"),
    ("placeholder", "{messages}")
])
model_get_schema = model_get_schema_prompt | llm.bind_tools([get_schema_tool])
print("Model get schema prompt defined.")

# Graph setup
workflow = StateGraph(State)
workflow.add_node("first_tool_call_node", first_tool_call)
workflow.add_node("list_tables_tool_node", create_tool_node_with_fallback([list_tables_tool]))
workflow.add_node("model_get_schema_node", 
    lambda state: {"messages": [model_get_schema.invoke({"messages": state["messages"], 
                                                        "table_list": state["messages"][-1].content})]})
workflow.add_node("get_schema_tool_node", create_tool_node_with_fallback([get_schema_tool]))
workflow.add_node("cache_check_node", cache_check_node)
workflow.add_node("query_gen_node", query_gen_node)
workflow.add_node("model_check_query_node", model_check_query_node)
workflow.add_node("execute_query_tool_node", create_tool_node_with_fallback([db_query_tool]))

workflow.add_edge(START, "first_tool_call_node")
workflow.add_edge("first_tool_call_node", "list_tables_tool_node")
workflow.add_edge("list_tables_tool_node", "model_get_schema_node")
workflow.add_edge("model_get_schema_node", "get_schema_tool_node")
workflow.add_edge("get_schema_tool_node", "cache_check_node")
workflow.add_edge("execute_query_tool_node", "query_gen_node")
workflow.add_conditional_edges(
    "cache_check_node",
    lambda state: "model_check_query_node" if state.get("cached_sql") else "query_gen_node",
    {
        "query_gen_node": "query_gen_node",
        "model_check_query_node": "model_check_query_node"
    }
)
workflow.add_conditional_edges(
    "query_gen_node",
    should_continue,
    {
        "model_check_query_node": "model_check_query_node",
        "query_gen_node": "query_gen_node",
        END: END
    }
)
workflow.add_edge("model_check_query_node", "execute_query_tool_node")

app_langgraph = workflow.compile()

def run_sql_query(request: QueryRequest):
    global last_query_rows, last_query_columns
    
    try:
        with sqlite3.connect(DB_NAME) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            if not tables:
                return QueryResponse(result="The database appears to be empty.", rows=[], columns=[])
            
            table_counts = {}
            for table in tables:
                table_name = table[0]
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    table_counts[table_name] = count
                except Exception:
                    continue
            
            if all(count == 0 for count in table_counts.values()):
                return QueryResponse(result="All tables in the database are empty.", rows=[], columns=[])
    
        query_input = {"messages": [HumanMessage(content=request.question)]}
        for event in app_langgraph.stream(query_input, {"recursion_limit": 50}):
            last_message_key = list(event.keys())[-1]
            
            if isinstance(event.get(last_message_key), dict) and event.get(last_message_key).get('messages'):
                msgs = event.get(last_message_key).get('messages')
                for msg in msgs:
                    if hasattr(msg, 'tool_calls') and msg.tool_calls:
                        tool_name = msg.tool_calls[0].get('name')
                        if tool_name == 'SubmitFinalAnswer':
                            final_answer = msg.tool_calls[0].get('args', {}).get('final_answer')
                            return QueryResponse(result=final_answer, rows=last_query_rows, columns=last_query_columns)
            
            if END in event:
                response = event[END]
                break
            else:
                response = event.get(last_message_key)
        
        if response and response.get("messages"):
            for msg in reversed(response["messages"]):
                if hasattr(msg, 'tool_call_id') and msg.tool_call_id.startswith("call_"):
                    tool_result = json.loads(msg.content) if msg.content.startswith("{") else {"result": msg.content}
                    return QueryResponse(result=tool_result["result"], rows=last_query_rows, columns=last_query_columns)
        
        return QueryResponse(result="No final response from agent.", rows=last_query_rows, columns=last_query_columns)
    
    except Exception as e:
        raise Exception(f"Error: {str(e)}")


def generate_insights(data_type: str = "campaign_performance",
                     filters: Optional[Dict[str, Any]] = None,
                     analysis_type: str = "summary") -> Dict[str, Any]:
    """
    Generate insights from marketing data.

    Args:
        data_type: Type of data to analyze ('campaign_performance', 'user_behavior', 'funnel_analysis')
        filters: Optional filters to apply to the data
        analysis_type: Type of analysis ('summary', 'detailed', 'predictive')

    Returns:
        Dictionary containing generated insights
    """
    try:
        # Sample questions based on data type
        sample_questions = {
            "campaign_performance": [
                "What are the top performing email campaigns by open rate?",
                "Show me the average click-through rates by campaign type",
                "Which campaigns have the highest conversion rates?"
            ],
            "user_behavior": [
                "What are the most common user journey stages?",
                "Show me user engagement patterns by demographics",
                "Which user segments have the highest lifetime value?"
            ],
            "funnel_analysis": [
                "Show me the conversion rates at each funnel stage",
                "What is the drop-off rate between awareness and consideration?",
                "Which stage has the highest abandonment rate?"
            ]
        }

        # Get appropriate question based on data type and analysis type
        questions = sample_questions.get(data_type, sample_questions["campaign_performance"])

        if analysis_type == "summary":
            question = questions[0]
        elif analysis_type == "detailed":
            question = questions[1] if len(questions) > 1 else questions[0]
        else:  # predictive
            question = questions[2] if len(questions) > 2 else questions[0]

        # Apply filters to question if provided
        if filters:
            filter_text = ", ".join([f"{k}={v}" for k, v in filters.items()])
            question += f" with filters: {filter_text}"

        # Run the query
        request = QueryRequest(question=question)
        response = run_sql_query(request)

        return {
            "data_type": data_type,
            "analysis_type": analysis_type,
            "question": question,
            "insights": response.result,
            "data_rows": response.rows,
            "columns": response.columns,
            "filters_applied": filters or {},
            "success": True
        }

    except Exception as e:
        return {
            "data_type": data_type,
            "analysis_type": analysis_type,
            "question": "",
            "insights": f"Error generating insights: {str(e)}",
            "data_rows": [],
            "columns": [],
            "filters_applied": filters or {},
            "success": False,
            "error": str(e)
        }


# Sample values for testing
SAMPLE_DATA_TYPES = ["campaign_performance", "user_behavior", "funnel_analysis"]
SAMPLE_ANALYSIS_TYPES = ["summary", "detailed", "predictive"]
SAMPLE_FILTERS = [
    {"campaign_type": "email", "date_range": "last_30_days"},
    {"user_segment": "premium", "region": "US"},
    {"product_category": "software", "stage": "consideration"}
]

SAMPLE_QUESTIONS = [
    "What are the top 5 performing email campaigns this month?",
    "Show me user engagement trends over the last quarter",
    "Which products have the highest conversion rates?",
    "What is the average time spent in each funnel stage?",
    "Show me the demographics of our most engaged users"
]


def main():
    """
    Main function for testing the insight agent functionality.
    """
    print("Testing Insight Agent...")

    # Test 1: Basic insight generation
    print("\n1. Testing basic insight generation:")
    result1 = generate_insights(
        data_type="campaign_performance",
        analysis_type="summary"
    )
    print(f"Result: {result1['success']}")
    print(f"Question: {result1['question']}")
    print(f"Insights: {result1['insights'][:200]}...")

    # Test 2: Insight generation with filters
    print("\n2. Testing insight generation with filters:")
    result2 = generate_insights(
        data_type="user_behavior",
        filters={"user_segment": "premium"},
        analysis_type="detailed"
    )
    print(f"Result: {result2['success']}")
    print(f"Question: {result2['question']}")
    print(f"Filters: {result2['filters_applied']}")

    # Test 3: Direct SQL query
    print("\n3. Testing direct SQL query:")
    try:
        request = QueryRequest(question="Show me all available tables in the database")
        response = run_sql_query(request)
        print(f"Query result: {response.result[:200]}...")
        print(f"Columns: {response.columns}")
        print(f"Row count: {len(response.rows)}")
    except Exception as e:
        print(f"Query failed: {str(e)}")

    print("\nInsight Agent testing completed.")


if __name__ == "__main__":
    main()